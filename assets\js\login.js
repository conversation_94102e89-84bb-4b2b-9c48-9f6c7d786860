// Login page specific JavaScript
class LoginManager {
    constructor() {
        this.currentStep = 'phone';
        this.phoneNumber = '';
        this.countryCode = '+966';
        this.verificationId = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupOTPInputs();
        this.hideLoadingOverlay();
    }

    setupEventListeners() {
        // Phone login form
        const phoneForm = document.getElementById('phone-login-form');
        if (phoneForm) {
            phoneForm.addEventListener('submit', (e) => this.handlePhoneSubmit(e));
        }

        // OTP form
        const otpForm = document.getElementById('otp-form');
        if (otpForm) {
            otpForm.addEventListener('submit', (e) => this.handleOTPSubmit(e));
        }

        // Google login
        const googleBtn = document.getElementById('google-login-btn');
        if (googleBtn) {
            googleBtn.addEventListener('click', () => this.handleGoogleLogin());
        }

        // Guest login
        const guestBtn = document.getElementById('guest-login-btn');
        if (guestBtn) {
            guestBtn.addEventListener('click', () => this.handleGuestLogin());
        }

        // Resend OTP
        const resendBtn = document.getElementById('resend-otp');
        if (resendBtn) {
            resendBtn.addEventListener('click', () => this.resendOTP());
        }

        // Change phone
        const changePhoneBtn = document.getElementById('change-phone');
        if (changePhoneBtn) {
            changePhoneBtn.addEventListener('click', () => this.changePhone());
        }

        // Country code change
        const countryCodeSelect = document.getElementById('country-code');
        if (countryCodeSelect) {
            countryCodeSelect.addEventListener('change', (e) => {
                this.countryCode = e.target.value;
            });
        }

        // Phone number input formatting
        const phoneInput = document.getElementById('phone-number');
        if (phoneInput) {
            phoneInput.addEventListener('input', (e) => this.formatPhoneNumber(e));
            phoneInput.addEventListener('keypress', (e) => this.validatePhoneInput(e));
        }
    }

    setupOTPInputs() {
        const otpInputs = document.querySelectorAll('.otp-digit');
        
        otpInputs.forEach((input, index) => {
            input.addEventListener('input', (e) => this.handleOTPInput(e, index));
            input.addEventListener('keydown', (e) => this.handleOTPKeydown(e, index));
            input.addEventListener('paste', (e) => this.handleOTPPaste(e));
        });
    }

    async handlePhoneSubmit(e) {
        e.preventDefault();
        
        const phoneInput = document.getElementById('phone-number');
        const phoneNumber = phoneInput.value.trim();
        
        if (!this.validatePhoneNumber(phoneNumber)) {
            this.showError('phoneNumber', 'رقم الهاتف غير صحيح');
            return;
        }

        this.phoneNumber = this.countryCode + phoneNumber;
        this.showLoadingOverlay('جاري إرسال رمز التحقق...');

        try {
            // Send OTP via Firebase Auth or your backend
            const result = await this.sendOTP(this.phoneNumber);
            this.verificationId = result.verificationId;
            
            this.hideLoadingOverlay();
            this.showOTPSection();
            this.showSuccess('تم إرسال رمز التحقق بنجاح');
            
        } catch (error) {
            this.hideLoadingOverlay();
            this.showError('general', 'فشل في إرسال رمز التحقق. حاول مرة أخرى.');
            console.error('OTP send error:', error);
        }
    }

    async handleOTPSubmit(e) {
        e.preventDefault();
        
        const otp = this.getOTPValue();
        
        if (otp.length !== 6) {
            this.showError('otp', 'يجب إدخال رمز التحقق كاملاً');
            return;
        }

        this.showLoadingOverlay('جاري التحقق من الرمز...');

        try {
            // Verify OTP
            const user = await this.verifyOTP(this.phoneNumber, otp);
            
            this.hideLoadingOverlay();
            this.showSuccess('تم تسجيل الدخول بنجاح');
            
            // Redirect to main page or dashboard
            setTimeout(() => {
                window.location.href = '../index.html';
            }, 1500);
            
        } catch (error) {
            this.hideLoadingOverlay();
            this.showError('otp', 'رمز التحقق غير صحيح');
            this.clearOTPInputs();
            console.error('OTP verification error:', error);
        }
    }

    async handleGoogleLogin() {
        this.showLoadingOverlay('جاري تسجيل الدخول بـ Google...');

        try {
            // Google Sign-In
            const user = await this.signInWithGoogle();
            
            this.hideLoadingOverlay();
            this.showSuccess('تم تسجيل الدخول بنجاح');
            
            setTimeout(() => {
                window.location.href = '../index.html';
            }, 1500);
            
        } catch (error) {
            this.hideLoadingOverlay();
            this.showError('general', 'فشل في تسجيل الدخول بـ Google');
            console.error('Google login error:', error);
        }
    }

    handleGuestLogin() {
        this.showLoadingOverlay('جاري الدخول كزائر...');
        
        // Set guest mode
        localStorage.setItem('isGuest', 'true');
        
        setTimeout(() => {
            this.hideLoadingOverlay();
            this.showSuccess('تم الدخول كزائر');
            
            setTimeout(() => {
                window.location.href = '../index.html';
            }, 1000);
        }, 1000);
    }

    async resendOTP() {
        const resendBtn = document.getElementById('resend-otp');
        resendBtn.disabled = true;
        resendBtn.textContent = 'جاري الإرسال...';

        try {
            await this.sendOTP(this.phoneNumber);
            this.showSuccess('تم إعادة إرسال رمز التحقق');
            
            // Start countdown
            this.startResendCountdown();
            
        } catch (error) {
            this.showError('general', 'فشل في إعادة إرسال الرمز');
            resendBtn.disabled = false;
            resendBtn.textContent = 'إعادة إرسال الرمز';
        }
    }

    startResendCountdown() {
        const resendBtn = document.getElementById('resend-otp');
        let countdown = 60;
        
        const timer = setInterval(() => {
            resendBtn.textContent = `إعادة الإرسال (${countdown}ث)`;
            countdown--;
            
            if (countdown < 0) {
                clearInterval(timer);
                resendBtn.disabled = false;
                resendBtn.textContent = 'إعادة إرسال الرمز';
            }
        }, 1000);
    }

    changePhone() {
        this.hideOTPSection();
        this.clearOTPInputs();
        document.getElementById('phone-number').focus();
    }

    showOTPSection() {
        const phoneSection = document.getElementById('phone-login');
        const otpSection = document.getElementById('otp-verification');
        const sentPhoneSpan = document.getElementById('sent-phone');
        
        phoneSection.style.display = 'none';
        otpSection.classList.remove('hidden');
        sentPhoneSpan.textContent = this.phoneNumber;
        
        // Focus first OTP input
        setTimeout(() => {
            document.querySelector('.otp-digit').focus();
        }, 100);
    }

    hideOTPSection() {
        const phoneSection = document.getElementById('phone-login');
        const otpSection = document.getElementById('otp-verification');
        
        phoneSection.style.display = 'block';
        otpSection.classList.add('hidden');
    }

    handleOTPInput(e, index) {
        const input = e.target;
        const value = input.value;
        
        // Only allow digits
        if (!/^\d$/.test(value)) {
            input.value = '';
            return;
        }
        
        input.classList.add('filled');
        
        // Move to next input
        if (value && index < 5) {
            const nextInput = document.querySelector(`[data-index="${index + 1}"]`);
            if (nextInput) {
                nextInput.focus();
            }
        }
        
        // Auto-submit if all fields are filled
        if (index === 5 && value) {
            setTimeout(() => {
                const otpForm = document.getElementById('otp-form');
                otpForm.dispatchEvent(new Event('submit'));
            }, 100);
        }
    }

    handleOTPKeydown(e, index) {
        // Handle backspace
        if (e.key === 'Backspace' && !e.target.value && index > 0) {
            const prevInput = document.querySelector(`[data-index="${index - 1}"]`);
            if (prevInput) {
                prevInput.focus();
                prevInput.value = '';
                prevInput.classList.remove('filled');
            }
        }
    }

    handleOTPPaste(e) {
        e.preventDefault();
        const paste = (e.clipboardData || window.clipboardData).getData('text');
        const digits = paste.replace(/\D/g, '').slice(0, 6);
        
        if (digits.length === 6) {
            const inputs = document.querySelectorAll('.otp-digit');
            digits.split('').forEach((digit, index) => {
                inputs[index].value = digit;
                inputs[index].classList.add('filled');
            });
            
            // Auto-submit
            setTimeout(() => {
                const otpForm = document.getElementById('otp-form');
                otpForm.dispatchEvent(new Event('submit'));
            }, 100);
        }
    }

    getOTPValue() {
        const inputs = document.querySelectorAll('.otp-digit');
        return Array.from(inputs).map(input => input.value).join('');
    }

    clearOTPInputs() {
        const inputs = document.querySelectorAll('.otp-digit');
        inputs.forEach(input => {
            input.value = '';
            input.classList.remove('filled');
        });
        inputs[0].focus();
    }

    formatPhoneNumber(e) {
        let value = e.target.value.replace(/\D/g, '');
        
        // Limit to 9 digits for Saudi numbers
        if (this.countryCode === '+966') {
            value = value.slice(0, 9);
        }
        
        e.target.value = value;
    }

    validatePhoneInput(e) {
        // Only allow digits
        if (!/\d/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'Enter'].includes(e.key)) {
            e.preventDefault();
        }
    }

    validatePhoneNumber(phoneNumber) {
        // Basic validation - adjust based on country
        if (this.countryCode === '+966') {
            return /^5\d{8}$/.test(phoneNumber);
        }
        return phoneNumber.length >= 8;
    }

    // Mock Firebase methods - replace with actual Firebase integration
    async sendOTP(phoneNumber) {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({ verificationId: 'mock-verification-id' });
            }, 2000);
        });
    }

    async verifyOTP(phoneNumber, otp) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                if (otp === '123456') { // Mock OTP
                    resolve({
                        uid: 'mock-uid',
                        phoneNumber: phoneNumber,
                        displayName: null
                    });
                } else {
                    reject(new Error('Invalid OTP'));
                }
            }, 1500);
        });
    }

    async signInWithGoogle() {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    uid: 'mock-google-uid',
                    email: '<EMAIL>',
                    displayName: 'مستخدم تجريبي'
                });
            }, 2000);
        });
    }

    // UI Helper methods
    showLoadingOverlay(text = 'جاري التحميل...') {
        const overlay = document.getElementById('loading-overlay');
        const loadingText = document.getElementById('loading-text');
        
        if (loadingText) loadingText.textContent = text;
        if (overlay) overlay.classList.remove('hidden');
    }

    hideLoadingOverlay() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) overlay.classList.add('hidden');
    }

    showError(field, message) {
        // Remove existing errors
        this.clearErrors();
        
        // Show error message
        if (window.sianaBaladi) {
            window.sianaBaladi.showNotification(message, 'error');
        } else {
            alert(message);
        }
        
        // Add error class to field
        if (field !== 'general') {
            const fieldElement = document.getElementById(field);
            if (fieldElement) {
                fieldElement.closest('.form-group')?.classList.add('error');
            }
        }
    }

    showSuccess(message) {
        if (window.sianaBaladi) {
            window.sianaBaladi.showNotification(message, 'success');
        } else {
            alert(message);
        }
    }

    clearErrors() {
        document.querySelectorAll('.form-group.error').forEach(group => {
            group.classList.remove('error');
        });
    }
}

// Initialize login manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.loginManager = new LoginManager();
});
