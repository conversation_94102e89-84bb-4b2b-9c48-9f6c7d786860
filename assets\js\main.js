// Main JavaScript file for Siana Baladi PWA
class SianaBaladi {
    constructor() {
        this.currentLanguage = 'ar';
        this.isOnline = navigator.onLine;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.hideLoadingScreen();
        this.updateStats();
        this.setupOfflineHandling();
        this.setupLanguageToggle();
        this.setupPWAInstall();
    }

    setupEventListeners() {
        // Navigation buttons
        const newReportBtn = document.getElementById('new-report-btn');
        const viewReportsBtn = document.getElementById('view-reports-btn');
        const loginBtn = document.getElementById('login-btn');
        const languageToggle = document.getElementById('language-toggle');

        if (newReportBtn) {
            newReportBtn.addEventListener('click', () => this.navigateToNewReport());
        }

        if (viewReportsBtn) {
            viewReportsBtn.addEventListener('click', () => this.navigateToReports());
        }

        if (loginBtn) {
            loginBtn.addEventListener('click', () => this.navigateToLogin());
        }

        if (languageToggle) {
            languageToggle.addEventListener('click', () => this.toggleLanguage());
        }

        // Online/Offline events
        window.addEventListener('online', () => this.handleOnlineStatus(true));
        window.addEventListener('offline', () => this.handleOnlineStatus(false));

        // PWA install prompt
        window.addEventListener('beforeinstallprompt', (e) => this.handleInstallPrompt(e));
    }

    hideLoadingScreen() {
        setTimeout(() => {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.classList.add('hidden');
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 300);
            }
        }, 1000);
    }

    async updateStats() {
        try {
            // This would normally fetch from Firebase
            const stats = await this.getStatsFromStorage();
            
            const totalReportsEl = document.getElementById('total-reports');
            const pendingReportsEl = document.getElementById('pending-reports');
            const completedReportsEl = document.getElementById('completed-reports');

            if (totalReportsEl) totalReportsEl.textContent = stats.total || 0;
            if (pendingReportsEl) pendingReportsEl.textContent = stats.pending || 0;
            if (completedReportsEl) completedReportsEl.textContent = stats.completed || 0;

            // Animate numbers
            this.animateNumbers();
        } catch (error) {
            console.error('Error updating stats:', error);
        }
    }

    async getStatsFromStorage() {
        // Mock data for now - would integrate with Firebase
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    total: Math.floor(Math.random() * 100) + 50,
                    pending: Math.floor(Math.random() * 30) + 10,
                    completed: Math.floor(Math.random() * 60) + 20
                });
            }, 500);
        });
    }

    animateNumbers() {
        const numberElements = document.querySelectorAll('.stat-content h3');
        
        numberElements.forEach(el => {
            const finalNumber = parseInt(el.textContent);
            let currentNumber = 0;
            const increment = finalNumber / 30;
            
            const timer = setInterval(() => {
                currentNumber += increment;
                if (currentNumber >= finalNumber) {
                    el.textContent = finalNumber;
                    clearInterval(timer);
                } else {
                    el.textContent = Math.floor(currentNumber);
                }
            }, 50);
        });
    }

    setupOfflineHandling() {
        this.isOnline = navigator.onLine;
        this.updateOnlineStatus();
    }

    handleOnlineStatus(isOnline) {
        this.isOnline = isOnline;
        this.updateOnlineStatus();
        
        if (isOnline) {
            this.syncOfflineData();
            this.showNotification('تم الاتصال بالإنترنت', 'success');
        } else {
            this.showNotification('أنت غير متصل بالإنترنت', 'warning');
        }
    }

    updateOnlineStatus() {
        const body = document.body;
        if (this.isOnline) {
            body.classList.remove('offline');
            body.classList.add('online');
        } else {
            body.classList.remove('online');
            body.classList.add('offline');
        }
    }

    async syncOfflineData() {
        try {
            const offlineReports = await this.getOfflineReports();
            if (offlineReports.length > 0) {
                this.showNotification(`جاري مزامنة ${offlineReports.length} بلاغ...`, 'info');
                // Trigger background sync
                if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
                    const registration = await navigator.serviceWorker.ready;
                    await registration.sync.register('background-sync-reports');
                }
            }
        } catch (error) {
            console.error('Error syncing offline data:', error);
        }
    }

    async getOfflineReports() {
        // This would get reports from IndexedDB
        return [];
    }

    setupLanguageToggle() {
        const savedLanguage = localStorage.getItem('language') || 'ar';
        this.setLanguage(savedLanguage);
    }

    toggleLanguage() {
        const newLanguage = this.currentLanguage === 'ar' ? 'en' : 'ar';
        this.setLanguage(newLanguage);
    }

    setLanguage(language) {
        this.currentLanguage = language;
        localStorage.setItem('language', language);
        
        const html = document.documentElement;
        if (language === 'ar') {
            html.setAttribute('lang', 'ar');
            html.setAttribute('dir', 'rtl');
        } else {
            html.setAttribute('lang', 'en');
            html.setAttribute('dir', 'ltr');
        }

        // Update text content based on language
        this.updateLanguageContent();
    }

    updateLanguageContent() {
        const translations = {
            ar: {
                'app-title': 'صيانة بلدي',
                'login': 'تسجيل الدخول',
                'new-report': 'إنشاء بلاغ جديد',
                'view-reports': 'عرض البلاغات',
                'loading': 'جاري التحميل...'
            },
            en: {
                'app-title': 'My City Maintenance',
                'login': 'Login',
                'new-report': 'New Report',
                'view-reports': 'View Reports',
                'loading': 'Loading...'
            }
        };

        // Update elements with data-translate attribute
        document.querySelectorAll('[data-translate]').forEach(el => {
            const key = el.getAttribute('data-translate');
            if (translations[this.currentLanguage][key]) {
                el.textContent = translations[this.currentLanguage][key];
            }
        });
    }

    setupPWAInstall() {
        let deferredPrompt;
        
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            this.showInstallButton();
        });

        window.addEventListener('appinstalled', () => {
            this.showNotification('تم تثبيت التطبيق بنجاح!', 'success');
            this.hideInstallButton();
        });
    }

    handleInstallPrompt(e) {
        e.preventDefault();
        this.deferredPrompt = e;
        this.showInstallButton();
    }

    showInstallButton() {
        // Create install button if it doesn't exist
        if (!document.getElementById('install-btn')) {
            const installBtn = document.createElement('button');
            installBtn.id = 'install-btn';
            installBtn.className = 'btn btn-secondary';
            installBtn.innerHTML = '<span class="material-icons">get_app</span> تثبيت التطبيق';
            installBtn.addEventListener('click', () => this.installPWA());
            
            const headerActions = document.querySelector('.header-actions');
            if (headerActions) {
                headerActions.insertBefore(installBtn, headerActions.firstChild);
            }
        }
    }

    hideInstallButton() {
        const installBtn = document.getElementById('install-btn');
        if (installBtn) {
            installBtn.remove();
        }
    }

    async installPWA() {
        if (this.deferredPrompt) {
            this.deferredPrompt.prompt();
            const { outcome } = await this.deferredPrompt.userChoice;
            
            if (outcome === 'accepted') {
                this.showNotification('جاري تثبيت التطبيق...', 'info');
            }
            
            this.deferredPrompt = null;
            this.hideInstallButton();
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="material-icons">${this.getNotificationIcon(type)}</span>
                <span>${message}</span>
            </div>
            <button class="notification-close">
                <span class="material-icons">close</span>
            </button>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);

        // Close button functionality
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        });
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'check_circle',
            error: 'error',
            warning: 'warning',
            info: 'info'
        };
        return icons[type] || 'info';
    }

    // Navigation methods
    navigateToNewReport() {
        window.location.href = 'pages/new-report.html';
    }

    navigateToReports() {
        window.location.href = 'pages/reports.html';
    }

    navigateToLogin() {
        window.location.href = 'pages/login.html';
    }

    navigateToMap() {
        window.location.href = 'pages/map.html';
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.sianaBaladi = new SianaBaladi();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SianaBaladi;
}
