rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isAdmin() {
      return request.auth.token.admin == true;
    }
    
    function isValidReport() {
      return request.resource.data.keys().hasAll(['title', 'description', 'location', 'type', 'status']) &&
             request.resource.data.title is string &&
             request.resource.data.title.size() > 0 &&
             request.resource.data.description is string &&
             request.resource.data.description.size() > 0 &&
             request.resource.data.location is map &&
             request.resource.data.location.keys().hasAll(['lat', 'lng']) &&
             request.resource.data.type in ['pothole', 'lighting', 'drainage', 'sidewalk', 'other'] &&
             request.resource.data.status in ['pending', 'in-progress', 'completed', 'rejected'];
    }
    
    function isValidUser() {
      return request.resource.data.keys().hasAll(['displayName', 'phoneNumber']) &&
             request.resource.data.displayName is string &&
             request.resource.data.phoneNumber is string;
    }
    
    // Users collection
    match /users/{userId} {
      // Users can read and write their own data
      allow read, write: if isAuthenticated() && isOwner(userId) && isValidUser();
      
      // Admins can read all users
      allow read: if isAuthenticated() && isAdmin();
    }
    
    // Reports collection
    match /reports/{reportId} {
      // Anyone can read reports (for public map view)
      allow read: if true;
      
      // Authenticated users can create reports
      allow create: if isAuthenticated() && 
                       isValidReport() &&
                       request.resource.data.userId == request.auth.uid &&
                       request.resource.data.status == 'pending';
      
      // Users can update their own reports (limited fields)
      allow update: if isAuthenticated() && 
                       isOwner(resource.data.userId) &&
                       // Only allow updating specific fields
                       request.resource.data.diff(resource.data).affectedKeys()
                         .hasOnly(['title', 'description', 'images', 'updatedAt']) &&
                       // Status cannot be changed by regular users
                       request.resource.data.status == resource.data.status;
      
      // Admins can update any report (including status changes)
      allow update: if isAuthenticated() && 
                       isAdmin() &&
                       isValidReport();
      
      // Only admins can delete reports
      allow delete: if isAuthenticated() && isAdmin();
    }
    
    // Comments subcollection under reports
    match /reports/{reportId}/comments/{commentId} {
      // Anyone can read comments
      allow read: if true;
      
      // Authenticated users can create comments
      allow create: if isAuthenticated() &&
                       request.resource.data.keys().hasAll(['text', 'userId', 'createdAt']) &&
                       request.resource.data.text is string &&
                       request.resource.data.text.size() > 0 &&
                       request.resource.data.userId == request.auth.uid;
      
      // Users can update their own comments
      allow update: if isAuthenticated() && 
                       isOwner(resource.data.userId) &&
                       request.resource.data.diff(resource.data).affectedKeys()
                         .hasOnly(['text', 'updatedAt']);
      
      // Users can delete their own comments, admins can delete any
      allow delete: if isAuthenticated() && 
                       (isOwner(resource.data.userId) || isAdmin());
    }
    
    // Statistics collection (read-only for users, write for admins/functions)
    match /statistics/{statId} {
      allow read: if true;
      allow write: if isAuthenticated() && isAdmin();
    }
    
    // Notifications collection
    match /notifications/{notificationId} {
      // Users can read their own notifications
      allow read: if isAuthenticated() && isOwner(resource.data.userId);
      
      // Only system/admins can create notifications
      allow create: if isAuthenticated() && isAdmin();
      
      // Users can mark their notifications as read
      allow update: if isAuthenticated() && 
                       isOwner(resource.data.userId) &&
                       request.resource.data.diff(resource.data).affectedKeys()
                         .hasOnly(['read', 'readAt']);
      
      // Users can delete their own notifications
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }
    
    // Admin-only collections
    match /admin/{document=**} {
      allow read, write: if isAuthenticated() && isAdmin();
    }
    
    // System logs (admin read-only)
    match /logs/{logId} {
      allow read: if isAuthenticated() && isAdmin();
      allow write: if false; // Only server-side functions can write logs
    }
    
    // Configuration collection (public read, admin write)
    match /config/{configId} {
      allow read: if true;
      allow write: if isAuthenticated() && isAdmin();
    }
    
    // Feedback collection
    match /feedback/{feedbackId} {
      // Authenticated users can create feedback
      allow create: if isAuthenticated() &&
                       request.resource.data.keys().hasAll(['message', 'type', 'userId']) &&
                       request.resource.data.message is string &&
                       request.resource.data.message.size() > 0 &&
                       request.resource.data.type in ['bug', 'feature', 'general'] &&
                       request.resource.data.userId == request.auth.uid;
      
      // Admins can read all feedback
      allow read: if isAuthenticated() && isAdmin();
      
      // Users can read their own feedback
      allow read: if isAuthenticated() && isOwner(resource.data.userId);
    }
    
    // Default deny rule for any other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
