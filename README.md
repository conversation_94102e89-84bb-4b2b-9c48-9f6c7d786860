هذا المشروع هو نسخة موقع ويب (Progressive Web App - PWA) لتطبيق صيانة بلدي، يتيح للمستخدمين الإبلاغ عن مشاكل الشوارع والحفر عبر المتصفح، مع دعم رفع الصور، تحديد الموقع على الخريطة، عرض البلاغات، والتفاعل معها.

الموقع مصمم للعمل عبر Cloudflare Pages ويوفر تجربة مستخدم مشابهة لتطبيق الموبايل، مع إمكانية التوسع لاحقًا للنشر كتطبيق على متجر Google Play.

الميزات الرئيسية
إنشاء بلاغ جديد مع رفع صور وتحديد موقع GPS أو يدوي.

عرض خريطة تفاعلية تحتوي على البلاغات مع تصنيفات وألوان.

قائمة بلاغات قابلة للفلترة والفرز.

تسجيل دخول برقم الهاتف عبر OTP، Google أو التصفح كزائر.

دعم حفظ البلاغ كمسودة عند انقطاع الإنترنت (وضع أوفلاين).

إرسال الإشعارات عند تحديث حالة البلاغ.

لوحة إدارة ويب منفصلة لإدارة البلاغات والحالات.

دعم RTL (العربية) والإنجليزية.

المتطلبات
حساب على Cloudflare لاستضافة الموقع (Cloudflare Pages).

حساب Firebase (أو بديل Backend) لتخزين البيانات والمصادقة.

مفتاح API لخدمات Google Maps مع تقييد الصلاحيات.

بيئة تطوير Node.js وFlutter Web (إذا تم بناء الواجهة بـ Flutter).