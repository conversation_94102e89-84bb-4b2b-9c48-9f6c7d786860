# صيانة بلدي - Progressive Web App

هذا المشروع هو تطبيق ويب تقدمي (PWA) لتطبيق صيانة بلدي، يتيح للمستخدمين الإبلاغ عن مشاكل الشوارع والحفر عبر المتصفح، مع دعم رفع الصور، تحديد الموقع على الخريطة، عرض البلاغات، والتفاعل معها.

الموقع مصمم للعمل عبر Cloudflare Pages ويوفر تجربة مستخدم مشابهة لتطبيق الموبايل، مع إمكانية التوسع لاحقًا للنشر كتطبيق على متجر Google Play.

## الميزات الرئيسية

- ✅ إنشاء بلاغ جديد مع رفع صور وتحديد موقع GPS أو يدوي
- ✅ عرض خريطة تفاعلية تحتوي على البلاغات مع تصنيفات وألوان
- ✅ قائمة بلاغات قابلة للفلترة والفرز
- ✅ تسجيل دخول برقم الهاتف عبر OTP، Google أو التصفح كزائر
- ✅ دعم حفظ البلاغ كمسودة عند انقطاع الإنترنت (وضع أوفلاين)
- ✅ إرسال الإشعارات عند تحديث حالة البلاغ
- ✅ لوحة إدارة ويب منفصلة لإدارة البلاغات والحالات
- ✅ دعم RTL (العربية) والإنجليزية

## بنية المشروع

```
صيانة بلدي/
├── index.html              # الصفحة الرئيسية
├── manifest.json           # ملف تكوين PWA
├── service-worker.js       # خدمة العمل للوضع الأوفلاين
├── assets/                 # الأصول والموارد
│   ├── images/            # الصور والأيقونات
│   ├── css/               # ملفات التنسيق
│   └── js/                # ملفات JavaScript
├── pages/                  # صفحات التطبيق
│   ├── login.html         # صفحة تسجيل الدخول
│   ├── reports.html       # صفحة البلاغات
│   ├── map.html           # صفحة الخريطة
│   └── admin.html         # لوحة الإدارة
└── config/                 # ملفات التكوين
    └── firebase-config.js  # تكوين Firebase
```

## المتطلبات

- حساب على Cloudflare لاستضافة الموقع (Cloudflare Pages)
- حساب Firebase للبيانات والمصادقة
- مفتاح API لخدمات Google Maps مع تقييد الصلاحيات
- متصفح حديث يدعم PWA features

## التقنيات المستخدمة

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **PWA**: Service Worker, Web App Manifest
- **Backend**: Firebase (Firestore, Authentication, Storage)
- **Maps**: Google Maps JavaScript API
- **Styling**: CSS Grid, Flexbox, CSS Custom Properties
- **Icons**: Material Design Icons

## الحالة الحالية للمشروع

### ✅ المكتمل
- [x] بنية المشروع الأساسية
- [x] ملفات HTML الرئيسية (index.html, login.html, reports.html, map.html)
- [x] نظام CSS مع دعم RTL كامل
- [x] JavaScript الأساسي للتفاعل والتنقل
- [x] تكوين PWA (manifest.json, service-worker.js)
- [x] نظام المصادقة الأساسي
- [x] واجهة تسجيل الدخول مع OTP
- [x] صفحة البلاغات مع الفلترة
- [x] صفحة الخريطة التفاعلية
- [x] دعم الوضع الأوفلاين
- [x] نظام الإشعارات

### 🔄 قيد التطوير
- [ ] تكامل Firebase الكامل
- [ ] تكامل Google Maps API
- [ ] نظام رفع الصور
- [ ] صفحة إنشاء بلاغ جديد
- [ ] لوحة الإدارة

### 📋 المطلوب للتشغيل
1. **إعداد Firebase**:
   - إنشاء مشروع Firebase جديد
   - تفعيل Authentication (Phone, Google)
   - إنشاء قاعدة بيانات Firestore
   - تفعيل Storage للصور
   - تحديث `config/firebase-config.js` بالمفاتيح الصحيحة

2. **إعداد Google Maps**:
   - الحصول على API Key من Google Cloud Console
   - تفعيل Maps JavaScript API
   - تحديث `pages/map.html` بالمفتاح الصحيح

3. **تثبيت التبعيات**:
   ```bash
   npm install
   ```

4. **تشغيل الخادم المحلي**:
   ```bash
   npm start
   ```

## التثبيت والتشغيل

### المتطلبات الأساسية
- Node.js (الإصدار 16 أو أحدث)
- حساب Firebase
- مفتاح Google Maps API

### خطوات التثبيت

1. **استنساخ المشروع**:
   ```bash
   git clone https://github.com/your-username/siana-baladi-pwa.git
   cd siana-baladi-pwa
   ```

2. **تثبيت التبعيات**:
   ```bash
   npm install
   ```

3. **إعداد Firebase**:
   - إنشاء مشروع جديد في [Firebase Console](https://console.firebase.google.com)
   - تفعيل Authentication وإضافة Phone و Google providers
   - إنشاء قاعدة بيانات Firestore
   - تفعيل Storage
   - نسخ إعدادات Firebase إلى `config/firebase-config.js`

4. **إعداد Google Maps**:
   - الحصول على API Key من [Google Cloud Console](https://console.cloud.google.com)
   - تفعيل Maps JavaScript API و Places API
   - إضافة المفتاح في `pages/map.html`

5. **تشغيل الخادم المحلي**:
   ```bash
   npm start
   ```

6. **فتح التطبيق**:
   - افتح المتصفح على `http://localhost:3000`

## الأوامر المتاحة

- `npm start` - تشغيل خادم التطوير
- `npm run build` - بناء الملفات للإنتاج
- `npm test` - تشغيل الاختبارات
- `npm run lint` - فحص جودة الكود
- `npm run lighthouse` - تدقيق PWA
