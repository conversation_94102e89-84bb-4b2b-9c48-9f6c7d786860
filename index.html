<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="تطبيق صيانة بلدي - أبلغ عن مشاكل الشوارع والحفر">
    <meta name="theme-color" content="#2196F3">
    
    <!-- PWA Meta Tags -->
    <link rel="manifest" href="manifest.json">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="صيانة بلدي">
    
    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="192x192" href="assets/images/app_logo.png">
    <link rel="apple-touch-icon" href="assets/images/app_logo.png">
    
    <title>صيانة بلدي - الصفحة الرئيسية</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/rtl.css">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Arabic Font -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner">
            <img src="assets/images/app_logo.png" alt="صيانة بلدي" class="loading-logo">
            <div class="spinner"></div>
            <p>جاري التحميل...</p>
        </div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <img src="assets/images/app_logo.png" alt="صيانة بلدي" class="logo">
                <h1>صيانة بلدي</h1>
                <div class="header-actions">
                    <button id="language-toggle" class="btn-icon" title="تغيير اللغة">
                        <span class="material-icons">language</span>
                    </button>
                    <button id="login-btn" class="btn-primary">تسجيل الدخول</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Hero Section -->
            <section class="hero">
                <div class="hero-content">
                    <h2>أبلغ عن مشاكل الشوارع والحفر</h2>
                    <p>ساعد في تحسين مدينتك من خلال الإبلاغ عن المشاكل التي تواجهها في الشوارع</p>
                    <div class="hero-actions">
                        <button id="new-report-btn" class="btn-primary btn-large">
                            <span class="material-icons">add_circle</span>
                            إنشاء بلاغ جديد
                        </button>
                        <button id="view-reports-btn" class="btn-secondary btn-large">
                            <span class="material-icons">list</span>
                            عرض البلاغات
                        </button>
                    </div>
                </div>
                <div class="hero-image">
                    <img src="assets/images/login_background.png" alt="صيانة الشوارع">
                </div>
            </section>

            <!-- Quick Stats -->
            <section class="stats">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <span class="material-icons">report</span>
                        </div>
                        <div class="stat-content">
                            <h3 id="total-reports">0</h3>
                            <p>إجمالي البلاغات</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <span class="material-icons">pending</span>
                        </div>
                        <div class="stat-content">
                            <h3 id="pending-reports">0</h3>
                            <p>بلاغات قيد المراجعة</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <span class="material-icons">check_circle</span>
                        </div>
                        <div class="stat-content">
                            <h3 id="completed-reports">0</h3>
                            <p>بلاغات مكتملة</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Features Section -->
            <section class="features">
                <h2>الميزات الرئيسية</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <span class="material-icons">camera_alt</span>
                        </div>
                        <h3>رفع الصور</h3>
                        <p>أضف صور واضحة للمشكلة لتسهيل عملية الإصلاح</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <span class="material-icons">location_on</span>
                        </div>
                        <h3>تحديد الموقع</h3>
                        <p>حدد موقع المشكلة بدقة باستخدام GPS أو الخريطة</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <span class="material-icons">map</span>
                        </div>
                        <h3>خريطة تفاعلية</h3>
                        <p>اعرض جميع البلاغات على خريطة تفاعلية</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <span class="material-icons">notifications</span>
                        </div>
                        <h3>إشعارات فورية</h3>
                        <p>احصل على إشعارات عند تحديث حالة بلاغك</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>صيانة بلدي</h3>
                    <p>تطبيق للإبلاغ عن مشاكل الشوارع والحفر</p>
                </div>
                <div class="footer-section">
                    <h3>روابط سريعة</h3>
                    <ul>
                        <li><a href="pages/reports.html">البلاغات</a></li>
                        <li><a href="pages/map.html">الخريطة</a></li>
                        <li><a href="pages/login.html">تسجيل الدخول</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>تواصل معنا</h3>
                    <p>للدعم الفني والاستفسارات</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 صيانة بلدي. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript Files -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/auth.js"></script>
    <script src="config/firebase-config.js"></script>
    
    <!-- Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('service-worker.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
</body>
</html>
