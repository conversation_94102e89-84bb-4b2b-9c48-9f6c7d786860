<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="البلاغات - صيانة بلدي">
    <meta name="theme-color" content="#2196F3">
    
    <title>البلاغات - صيانة بلدي</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/rtl.css">
    <link rel="stylesheet" href="../assets/css/reports.css">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Arabic Font -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="reports-page">
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <button class="btn-icon back-btn" onclick="history.back()">
                    <span class="material-icons">arrow_forward</span>
                </button>
                <div class="header-title">
                    <h1>البلاغات</h1>
                </div>
                <div class="header-actions">
                    <button id="filter-btn" class="btn-icon" title="فلترة">
                        <span class="material-icons">filter_list</span>
                    </button>
                    <button id="search-btn" class="btn-icon" title="بحث">
                        <span class="material-icons">search</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Search Bar -->
    <div id="search-bar" class="search-bar hidden">
        <div class="container">
            <div class="search-input-container">
                <span class="material-icons">search</span>
                <input type="text" id="search-input" placeholder="ابحث في البلاغات...">
                <button id="clear-search" class="btn-icon">
                    <span class="material-icons">clear</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Filter Panel -->
    <div id="filter-panel" class="filter-panel hidden">
        <div class="container">
            <div class="filter-content">
                <div class="filter-group">
                    <label>الحالة</label>
                    <div class="filter-options">
                        <label class="filter-option">
                            <input type="checkbox" value="pending" checked>
                            <span class="checkmark"></span>
                            قيد المراجعة
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" value="in-progress" checked>
                            <span class="checkmark"></span>
                            قيد التنفيذ
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" value="completed" checked>
                            <span class="checkmark"></span>
                            مكتمل
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" value="rejected" checked>
                            <span class="checkmark"></span>
                            مرفوض
                        </label>
                    </div>
                </div>
                <div class="filter-group">
                    <label>نوع المشكلة</label>
                    <div class="filter-options">
                        <label class="filter-option">
                            <input type="checkbox" value="pothole" checked>
                            <span class="checkmark"></span>
                            حفرة في الطريق
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" value="lighting" checked>
                            <span class="checkmark"></span>
                            إضاءة الشارع
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" value="drainage" checked>
                            <span class="checkmark"></span>
                            تصريف المياه
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" value="sidewalk" checked>
                            <span class="checkmark"></span>
                            الرصيف
                        </label>
                        <label class="filter-option">
                            <input type="checkbox" value="other" checked>
                            <span class="checkmark"></span>
                            أخرى
                        </label>
                    </div>
                </div>
                <div class="filter-actions">
                    <button class="btn btn-primary" id="apply-filters">تطبيق الفلاتر</button>
                    <button class="btn btn-secondary" id="clear-filters">مسح الفلاتر</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Reports Summary -->
            <div class="reports-summary">
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-number" id="total-count">0</span>
                        <span class="stat-label">إجمالي البلاغات</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="filtered-count">0</span>
                        <span class="stat-label">البلاغات المعروضة</span>
                    </div>
                </div>
                <div class="view-toggle">
                    <button class="view-btn active" data-view="list">
                        <span class="material-icons">view_list</span>
                    </button>
                    <button class="view-btn" data-view="grid">
                        <span class="material-icons">view_module</span>
                    </button>
                    <button class="view-btn" data-view="map">
                        <span class="material-icons">map</span>
                    </button>
                </div>
            </div>

            <!-- Reports List -->
            <div id="reports-container" class="reports-container">
                <div id="reports-list" class="reports-list">
                    <!-- Reports will be loaded here -->
                </div>
                
                <!-- Loading Indicator -->
                <div id="loading-indicator" class="loading-indicator">
                    <div class="spinner"></div>
                    <p>جاري تحميل البلاغات...</p>
                </div>
                
                <!-- Empty State -->
                <div id="empty-state" class="empty-state hidden">
                    <div class="empty-icon">
                        <span class="material-icons">inbox</span>
                    </div>
                    <h3>لا توجد بلاغات</h3>
                    <p>لم يتم العثور على بلاغات تطابق معايير البحث</p>
                    <button class="btn btn-primary" onclick="window.location.href='new-report.html'">
                        <span class="material-icons">add</span>
                        إنشاء بلاغ جديد
                    </button>
                </div>
            </div>

            <!-- Load More Button -->
            <div class="load-more-container">
                <button id="load-more-btn" class="btn btn-secondary hidden">
                    تحميل المزيد
                </button>
            </div>
        </div>
    </main>

    <!-- Floating Action Button -->
    <button class="fab" onclick="window.location.href='new-report.html'" title="إنشاء بلاغ جديد">
        <span class="material-icons">add</span>
    </button>

    <!-- Report Details Modal -->
    <div id="report-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">تفاصيل البلاغ</h3>
                <button class="modal-close" id="close-modal">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Report details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/auth.js"></script>
    <script src="../assets/js/reports.js"></script>
    <script src="../config/firebase-config.js"></script>
</body>
</html>
