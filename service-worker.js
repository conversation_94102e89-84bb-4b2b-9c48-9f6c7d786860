const CACHE_NAME = 'siana-baladi-v1';
const urlsToCache = [
  '/',
  '/index.html',
  '/pages/login.html',
  '/pages/reports.html',
  '/pages/map.html',
  '/pages/new-report.html',
  '/assets/css/main.css',
  '/assets/css/rtl.css',
  '/assets/js/main.js',
  '/assets/js/auth.js',
  '/assets/js/reports.js',
  '/assets/js/map.js',
  '/assets/images/app_logo.png',
  '/assets/images/login_background.png',
  '/config/firebase-config.js',
  'https://fonts.googleapis.com/icon?family=Material+Icons',
  'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap'
];

// Install event - cache resources
self.addEventListener('install', event => {
  console.log('Service Worker: Install');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Service Worker: Caching files');
        return cache.addAll(urlsToCache);
      })
      .then(() => self.skipWaiting())
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('Service Worker: Activate');
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME) {
            console.log('Service Worker: Deleting old cache', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => self.clients.claim())
  );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', event => {
  console.log('Service Worker: Fetch', event.request.url);
  
  // Handle API requests differently
  if (event.request.url.includes('firestore.googleapis.com') || 
      event.request.url.includes('firebase.googleapis.com')) {
    // For Firebase requests, try network first, then show offline message
    event.respondWith(
      fetch(event.request)
        .catch(() => {
          return new Response(
            JSON.stringify({ 
              error: 'offline', 
              message: 'أنت غير متصل بالإنترنت. سيتم حفظ البيانات محلياً.' 
            }),
            {
              headers: { 'Content-Type': 'application/json' }
            }
          );
        })
    );
    return;
  }

  // For other requests, use cache-first strategy
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // Return cached version or fetch from network
        return response || fetch(event.request);
      })
      .catch(() => {
        // If both cache and network fail, return offline page
        if (event.request.destination === 'document') {
          return caches.match('/index.html');
        }
      })
  );
});

// Background sync for offline reports
self.addEventListener('sync', event => {
  console.log('Service Worker: Background Sync', event.tag);
  
  if (event.tag === 'background-sync-reports') {
    event.waitUntil(syncOfflineReports());
  }
});

// Push notification handler
self.addEventListener('push', event => {
  console.log('Service Worker: Push notification received');
  
  const options = {
    body: event.data ? event.data.text() : 'تم تحديث حالة بلاغك',
    icon: '/assets/images/app_logo.png',
    badge: '/assets/images/app_logo.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'عرض البلاغ',
        icon: '/assets/images/app_logo.png'
      },
      {
        action: 'close',
        title: 'إغلاق',
        icon: '/assets/images/app_logo.png'
      }
    ],
    requireInteraction: true,
    silent: false
  };

  event.waitUntil(
    self.registration.showNotification('صيانة بلدي', options)
  );
});

// Notification click handler
self.addEventListener('notificationclick', event => {
  console.log('Service Worker: Notification click received');
  
  event.notification.close();

  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/pages/reports.html')
    );
  } else if (event.action === 'close') {
    // Just close the notification
    return;
  } else {
    // Default action - open the app
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

// Function to sync offline reports
async function syncOfflineReports() {
  try {
    const offlineReports = await getOfflineReports();
    
    for (const report of offlineReports) {
      try {
        await submitReport(report);
        await removeOfflineReport(report.id);
        console.log('Offline report synced:', report.id);
      } catch (error) {
        console.error('Failed to sync offline report:', error);
      }
    }
  } catch (error) {
    console.error('Background sync failed:', error);
  }
}

// Helper functions for offline storage
function getOfflineReports() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('SianaBaladi', 1);
    
    request.onerror = () => reject(request.error);
    
    request.onsuccess = () => {
      const db = request.result;
      const transaction = db.transaction(['offlineReports'], 'readonly');
      const store = transaction.objectStore('offlineReports');
      const getAllRequest = store.getAll();
      
      getAllRequest.onsuccess = () => resolve(getAllRequest.result);
      getAllRequest.onerror = () => reject(getAllRequest.error);
    };
  });
}

function removeOfflineReport(reportId) {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('SianaBaladi', 1);
    
    request.onerror = () => reject(request.error);
    
    request.onsuccess = () => {
      const db = request.result;
      const transaction = db.transaction(['offlineReports'], 'readwrite');
      const store = transaction.objectStore('offlineReports');
      const deleteRequest = store.delete(reportId);
      
      deleteRequest.onsuccess = () => resolve();
      deleteRequest.onerror = () => reject(deleteRequest.error);
    };
  });
}

async function submitReport(report) {
  // This would integrate with Firebase
  const response = await fetch('/api/reports', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(report)
  });
  
  if (!response.ok) {
    throw new Error('Failed to submit report');
  }
  
  return response.json();
}
