# دليل الإعداد - صيانة بلدي PWA

## نظرة عامة
هذا الدليل يوضح كيفية إعداد وتشغيل تطبيق صيانة بلدي PWA من البداية.

## المتطلبات الأساسية

### 1. البرامج المطلوبة
- **Node.js** (الإصدار 16 أو أحدث)
- **npm** أو **yarn**
- **Git**
- متصفح حديث (Chrome, Firefox, Safari, Edge)

### 2. الحسابات المطلوبة
- حساب [Firebase](https://firebase.google.com/)
- حساب [Google Cloud Platform](https://cloud.google.com/) للخرائط
- حساب [Cloudflare](https://cloudflare.com/) للاستضافة (اختياري)

## خطوات الإعداد التفصيلية

### الخطوة 1: إعد<PERSON> المشروع المحلي

```bash
# 1. استنساخ المشروع
git clone https://github.com/your-username/siana-baladi-pwa.git
cd siana-baladi-pwa

# 2. تثبيت التبعيات
npm install

# 3. التحقق من التثبيت
npm run lint
```

### الخطوة 2: إعداد Firebase

#### 2.1 إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. انقر على "إنشاء مشروع" أو "Create a project"
3. أدخل اسم المشروع: `siana-baladi`
4. اختر إعدادات Google Analytics (اختياري)
5. انقر على "إنشاء المشروع"

#### 2.2 إعداد Authentication
1. في لوحة تحكم Firebase، اذهب إلى **Authentication**
2. انقر على **Get started**
3. اذهب إلى تبويب **Sign-in method**
4. فعّل **Phone** provider:
   - انقر على Phone
   - فعّل الخيار
   - أضف أرقام الهواتف للاختبار (اختياري)
5. فعّل **Google** provider:
   - انقر على Google
   - فعّل الخيار
   - أدخل اسم المشروع وإيميل الدعم

#### 2.3 إعداد Firestore Database
1. اذهب إلى **Firestore Database**
2. انقر على **Create database**
3. اختر **Start in test mode** (للتطوير)
4. اختر الموقع الجغرافي (مثل: europe-west3)
5. انقر على **Done**

#### 2.4 إعداد Storage
1. اذهب إلى **Storage**
2. انقر على **Get started**
3. اختر **Start in test mode**
4. اختر نفس الموقع الجغرافي المستخدم في Firestore

#### 2.5 الحصول على إعدادات Firebase
1. اذهب إلى **Project Settings** (أيقونة الترس)
2. انتقل إلى تبويب **General**
3. في قسم **Your apps**، انقر على **Web app** (</>)
4. أدخل اسم التطبيق: `siana-baladi-web`
5. فعّل **Firebase Hosting** (اختياري)
6. انقر على **Register app**
7. انسخ كود التكوين

#### 2.6 تحديث ملف التكوين
افتح `config/firebase-config.js` وحدث المتغيرات:

```javascript
const firebaseConfig = {
    apiKey: "your-api-key-here",
    authDomain: "your-project.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project.appspot.com",
    messagingSenderId: "123456789",
    appId: "your-app-id",
    measurementId: "G-XXXXXXXXXX"
};
```

### الخطوة 3: إعداد Google Maps

#### 3.1 إنشاء مشروع Google Cloud
1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com)
2. إنشاء مشروع جديد أو استخدم مشروع موجود
3. فعّل الفوترة (Billing) للمشروع

#### 3.2 تفعيل APIs المطلوبة
1. اذهب إلى **APIs & Services** > **Library**
2. ابحث عن وفعّل:
   - **Maps JavaScript API**
   - **Places API**
   - **Geocoding API**
   - **Geolocation API**

#### 3.3 إنشاء API Key
1. اذهب إلى **APIs & Services** > **Credentials**
2. انقر على **Create Credentials** > **API Key**
3. انسخ المفتاح
4. انقر على **Restrict Key** لتقييد الاستخدام:
   - **Application restrictions**: HTTP referrers
   - أضف النطاقات المسموحة:
     - `http://localhost:3000/*`
     - `https://your-domain.com/*`
   - **API restrictions**: اختر APIs المحددة واختر APIs المفعلة

#### 3.4 تحديث ملف الخريطة
افتح `pages/map.html` وحدث:

```html
<script async defer 
    src="https://maps.googleapis.com/maps/api/js?key=YOUR_ACTUAL_API_KEY&callback=initMap&libraries=places&language=ar&region=SA">
</script>
```

### الخطوة 4: إعداد قواعد الأمان

#### 4.1 قواعد Firestore
في Firebase Console، اذهب إلى **Firestore Database** > **Rules**:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Reports are readable by all, writable by authenticated users
    match /reports/{reportId} {
      allow read: if true;
      allow create: if request.auth != null;
      allow update: if request.auth != null && 
        (request.auth.uid == resource.data.userId || 
         request.auth.token.admin == true);
      allow delete: if request.auth != null && 
        request.auth.token.admin == true;
    }
  }
}
```

#### 4.2 قواعد Storage
اذهب إلى **Storage** > **Rules**:

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /reports/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null &&
        request.resource.size < 5 * 1024 * 1024 &&
        request.resource.contentType.matches('image/.*');
    }
  }
}
```

### الخطوة 5: تشغيل التطبيق

```bash
# تشغيل خادم التطوير
npm start

# أو باستخدام live-server للتحديث التلقائي
npm run dev
```

افتح المتصفح على `http://localhost:3000`

### الخطوة 6: اختبار الميزات

#### 6.1 اختبار PWA
1. افتح أدوات المطور (F12)
2. اذهب إلى تبويب **Application**
3. تحقق من:
   - Service Worker مسجل
   - Manifest صحيح
   - Cache Storage يعمل

#### 6.2 اختبار المصادقة
1. اذهب إلى صفحة تسجيل الدخول
2. جرب تسجيل الدخول برقم الهاتف (استخدم أرقام الاختبار)
3. جرب تسجيل الدخول بـ Google
4. جرب الدخول كزائر

#### 6.3 اختبار الخريطة
1. اذهب إلى صفحة الخريطة
2. تأكد من تحميل الخريطة
3. جرب تحديد الموقع

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ Firebase
```
Firebase: Error (auth/configuration-not-found)
```
**الحل**: تأكد من صحة إعدادات Firebase في `config/firebase-config.js`

#### 2. خطأ Google Maps
```
Google Maps JavaScript API error: RefererNotAllowedMapError
```
**الحل**: تأكد من إضافة النطاق في قيود API Key

#### 3. خطأ Service Worker
```
Service Worker registration failed
```
**الحل**: تأكد من تشغيل التطبيق على HTTPS أو localhost

#### 4. مشاكل RTL
**الحل**: تأكد من إضافة `dir="rtl"` و `lang="ar"` في HTML

### أدوات التشخيص

```bash
# فحص جودة الكود
npm run lint

# تدقيق PWA
npm run lighthouse

# اختبار HTML
npm run validate-html
```

## النشر للإنتاج

### 1. بناء الملفات
```bash
npm run build
```

### 2. النشر على Firebase Hosting
```bash
# تثبيت Firebase CLI
npm install -g firebase-tools

# تسجيل الدخول
firebase login

# تهيئة المشروع
firebase init hosting

# النشر
firebase deploy
```

### 3. النشر على Cloudflare Pages
1. ربط المستودع بـ Cloudflare Pages
2. إعداد أوامر البناء: `npm run build`
3. مجلد الإخراج: `./`

## الدعم والمساعدة

- **الوثائق**: راجع ملف README.md
- **المشاكل**: أنشئ issue في GitHub
- **المساهمة**: راجع CONTRIBUTING.md

## الخطوات التالية

بعد إكمال الإعداد، يمكنك:
1. تخصيص التصميم والألوان
2. إضافة ميزات جديدة
3. تحسين الأداء
4. إضافة اختبارات
5. إعداد CI/CD
