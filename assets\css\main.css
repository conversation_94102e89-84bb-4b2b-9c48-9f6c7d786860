/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-color: #2196F3;
    --primary-dark: #1976D2;
    --primary-light: #BBDEFB;
    --secondary-color: #FF9800;
    --success-color: #4CAF50;
    --warning-color: #FFC107;
    --error-color: #F44336;
    --info-color: #00BCD4;
    
    /* Neutral Colors */
    --text-primary: #212121;
    --text-secondary: #757575;
    --text-disabled: #BDBDBD;
    --background-primary: #FFFFFF;
    --background-secondary: #FAFAFA;
    --background-card: #FFFFFF;
    --border-color: #E0E0E0;
    --shadow-color: rgba(0, 0, 0, 0.1);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* Typography */
    --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 2rem;
    
    /* Border Radius */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 1rem;
    --border-radius-xl: 1.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 var(--shadow-color);
    --shadow-md: 0 4px 6px -1px var(--shadow-color);
    --shadow-lg: 0 10px 15px -3px var(--shadow-color);
    --shadow-xl: 0 20px 25px -5px var(--shadow-color);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-secondary);
    direction: rtl;
    text-align: right;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--background-primary);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity var(--transition-normal);
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-spinner {
    text-align: center;
}

.loading-logo {
    width: 80px;
    height: 80px;
    margin-bottom: var(--spacing-md);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--primary-light);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: var(--spacing-md) auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Header */
.header {
    background-color: var(--background-primary);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) 0;
}

.logo {
    width: 40px;
    height: 40px;
    margin-left: var(--spacing-sm);
}

.header h1 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--primary-color);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--border-radius-md);
    font-family: inherit;
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-icon {
    padding: var(--spacing-sm);
    background-color: transparent;
    color: var(--text-secondary);
    border-radius: 50%;
}

.btn-icon:hover {
    background-color: var(--background-secondary);
    color: var(--primary-color);
}

.btn-large {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 200px);
    padding: var(--spacing-xl) 0;
}

/* Hero Section */
.hero {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxl);
    align-items: center;
    margin-bottom: var(--spacing-xxl);
    padding: var(--spacing-xxl) 0;
}

.hero-content h2 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    line-height: 1.2;
}

.hero-content p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: 1.6;
}

.hero-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

/* Stats Section */
.stats {
    margin-bottom: var(--spacing-xxl);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.stat-card {
    background-color: var(--background-card);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: transform var(--transition-fast);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background-color: var(--primary-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-icon .material-icons {
    font-size: 2rem;
    color: var(--primary-color);
}

.stat-content h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stat-content p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Features Section */
.features {
    margin-bottom: var(--spacing-xxl);
}

.features h2 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    text-align: center;
    margin-bottom: var(--spacing-xl);
    color: var(--text-primary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.feature-card {
    background-color: var(--background-card);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    text-align: center;
    transition: transform var(--transition-fast);
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
}

.feature-icon .material-icons {
    font-size: 2.5rem;
    color: white;
}

.feature-card h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Footer */
.footer {
    background-color: var(--text-primary);
    color: white;
    padding: var(--spacing-xxl) 0 var(--spacing-md);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-section h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--primary-light);
}

.footer-section p {
    color: #CCCCCC;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-section ul li a {
    color: #CCCCCC;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--primary-light);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-md);
    border-top: 1px solid #444;
    color: #CCCCCC;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-xl);
    }
    
    .hero-actions {
        justify-content: center;
    }
    
    .hero-content h2 {
        font-size: var(--font-size-2xl);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .header-content {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
    
    .header-actions {
        order: 3;
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .btn-large {
        padding: var(--spacing-md);
        font-size: var(--font-size-base);
    }
}
