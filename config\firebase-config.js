// Firebase Configuration for Siana Baladi <PERSON>
// Replace with your actual Firebase config

const firebaseConfig = {
    apiKey: "YOUR_API_KEY",
    authDomain: "siana-baladi.firebaseapp.com",
    projectId: "siana-baladi",
    storageBucket: "siana-baladi.appspot.com",
    messagingSenderId: "123456789",
    appId: "1:123456789:web:abcdef123456",
    measurementId: "G-XXXXXXXXXX"
};

// Initialize Firebase
let app, auth, db, storage, messaging;

// Check if Firebase is available
if (typeof firebase !== 'undefined') {
    try {
        // Initialize Firebase
        app = firebase.initializeApp(firebaseConfig);
        
        // Initialize Firebase services
        auth = firebase.auth();
        db = firebase.firestore();
        storage = firebase.storage();
        
        // Initialize messaging if supported
        if (firebase.messaging.isSupported()) {
            messaging = firebase.messaging();
        }
        
        console.log('Firebase initialized successfully');
        
        // Configure Firestore settings
        db.settings({
            cacheSizeBytes: firebase.firestore.CACHE_SIZE_UNLIMITED
        });
        
        // Enable offline persistence
        db.enablePersistence({
            synchronizeTabs: true
        }).catch((err) => {
            if (err.code == 'failed-precondition') {
                console.warn('Multiple tabs open, persistence can only be enabled in one tab at a time.');
            } else if (err.code == 'unimplemented') {
                console.warn('The current browser does not support all of the features required to enable persistence');
            }
        });
        
    } catch (error) {
        console.error('Error initializing Firebase:', error);
    }
} else {
    console.warn('Firebase SDK not loaded');
}

// Firebase Authentication Configuration
if (auth) {
    // Configure auth settings
    auth.languageCode = 'ar';
    
    // Set up auth state listener
    auth.onAuthStateChanged((user) => {
        if (user) {
            console.log('User signed in:', user.uid);
            // Dispatch custom event for auth state change
            document.dispatchEvent(new CustomEvent('authStateChanged', {
                detail: {
                    uid: user.uid,
                    email: user.email,
                    displayName: user.displayName,
                    phoneNumber: user.phoneNumber,
                    photoURL: user.photoURL,
                    emailVerified: user.emailVerified
                }
            }));
        } else {
            console.log('User signed out');
            document.dispatchEvent(new CustomEvent('authStateChanged', {
                detail: null
            }));
        }
    });
}

// Firebase Cloud Messaging Configuration
if (messaging) {
    // Request permission for notifications
    const requestNotificationPermission = async () => {
        try {
            const permission = await Notification.requestPermission();
            if (permission === 'granted') {
                console.log('Notification permission granted');
                
                // Get FCM token
                const token = await messaging.getToken({
                    vapidKey: 'YOUR_VAPID_KEY' // Replace with your VAPID key
                });
                
                if (token) {
                    console.log('FCM Token:', token);
                    // Save token to user profile in Firestore
                    await saveFCMToken(token);
                }
            } else {
                console.log('Notification permission denied');
            }
        } catch (error) {
            console.error('Error requesting notification permission:', error);
        }
    };
    
    // Handle foreground messages
    messaging.onMessage((payload) => {
        console.log('Message received in foreground:', payload);
        
        // Show custom notification
        if (window.sianaBaladi) {
            window.sianaBaladi.showNotification(
                payload.notification.body,
                'info'
            );
        }
    });
    
    // Handle token refresh
    messaging.onTokenRefresh(async () => {
        try {
            const refreshedToken = await messaging.getToken({
                vapidKey: 'YOUR_VAPID_KEY'
            });
            console.log('Token refreshed:', refreshedToken);
            await saveFCMToken(refreshedToken);
        } catch (error) {
            console.error('Error refreshing token:', error);
        }
    });
    
    // Auto-request notification permission on page load
    if ('Notification' in window && Notification.permission === 'default') {
        setTimeout(requestNotificationPermission, 3000);
    }
}

// Helper Functions
async function saveFCMToken(token) {
    if (!auth.currentUser || !db) return;
    
    try {
        await db.collection('users').doc(auth.currentUser.uid).set({
            fcmToken: token,
            lastTokenUpdate: firebase.firestore.FieldValue.serverTimestamp()
        }, { merge: true });
        
        console.log('FCM token saved to Firestore');
    } catch (error) {
        console.error('Error saving FCM token:', error);
    }
}

// Database helper functions
const FirebaseAPI = {
    // Reports Collection
    reports: {
        // Create a new report
        async create(reportData) {
            if (!db) throw new Error('Firestore not initialized');
            
            const docRef = await db.collection('reports').add({
                ...reportData,
                createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                updatedAt: firebase.firestore.FieldValue.serverTimestamp(),
                userId: auth.currentUser?.uid || null,
                status: 'pending'
            });
            
            return docRef.id;
        },
        
        // Get all reports with pagination
        async getAll(limit = 20, lastDoc = null) {
            if (!db) throw new Error('Firestore not initialized');
            
            let query = db.collection('reports')
                .orderBy('createdAt', 'desc')
                .limit(limit);
            
            if (lastDoc) {
                query = query.startAfter(lastDoc);
            }
            
            const snapshot = await query.get();
            const reports = [];
            
            snapshot.forEach(doc => {
                reports.push({
                    id: doc.id,
                    ...doc.data()
                });
            });
            
            return {
                reports,
                lastDoc: snapshot.docs[snapshot.docs.length - 1]
            };
        },
        
        // Get report by ID
        async getById(reportId) {
            if (!db) throw new Error('Firestore not initialized');
            
            const doc = await db.collection('reports').doc(reportId).get();
            
            if (doc.exists) {
                return {
                    id: doc.id,
                    ...doc.data()
                };
            }
            
            return null;
        },
        
        // Update report
        async update(reportId, updateData) {
            if (!db) throw new Error('Firestore not initialized');
            
            await db.collection('reports').doc(reportId).update({
                ...updateData,
                updatedAt: firebase.firestore.FieldValue.serverTimestamp()
            });
        },
        
        // Delete report
        async delete(reportId) {
            if (!db) throw new Error('Firestore not initialized');
            
            await db.collection('reports').doc(reportId).delete();
        },
        
        // Get reports by user
        async getByUser(userId, limit = 20) {
            if (!db) throw new Error('Firestore not initialized');
            
            const snapshot = await db.collection('reports')
                .where('userId', '==', userId)
                .orderBy('createdAt', 'desc')
                .limit(limit)
                .get();
            
            const reports = [];
            snapshot.forEach(doc => {
                reports.push({
                    id: doc.id,
                    ...doc.data()
                });
            });
            
            return reports;
        },
        
        // Get reports by location (within radius)
        async getByLocation(lat, lng, radiusKm = 5) {
            if (!db) throw new Error('Firestore not initialized');
            
            // This is a simplified version - for production, use GeoFirestore
            const snapshot = await db.collection('reports')
                .where('location.lat', '>=', lat - (radiusKm / 111))
                .where('location.lat', '<=', lat + (radiusKm / 111))
                .get();
            
            const reports = [];
            snapshot.forEach(doc => {
                const data = doc.data();
                const distance = calculateDistance(lat, lng, data.location.lat, data.location.lng);
                
                if (distance <= radiusKm) {
                    reports.push({
                        id: doc.id,
                        ...data,
                        distance
                    });
                }
            });
            
            return reports.sort((a, b) => a.distance - b.distance);
        }
    },
    
    // Users Collection
    users: {
        async create(userData) {
            if (!db || !auth.currentUser) throw new Error('Not authenticated');
            
            await db.collection('users').doc(auth.currentUser.uid).set({
                ...userData,
                createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                updatedAt: firebase.firestore.FieldValue.serverTimestamp()
            });
        },
        
        async get(userId) {
            if (!db) throw new Error('Firestore not initialized');
            
            const doc = await db.collection('users').doc(userId).get();
            return doc.exists ? doc.data() : null;
        },
        
        async update(userId, updateData) {
            if (!db) throw new Error('Firestore not initialized');
            
            await db.collection('users').doc(userId).update({
                ...updateData,
                updatedAt: firebase.firestore.FieldValue.serverTimestamp()
            });
        }
    },
    
    // Storage helper
    storage: {
        async uploadImage(file, path) {
            if (!storage) throw new Error('Storage not initialized');
            
            const storageRef = storage.ref().child(path);
            const snapshot = await storageRef.put(file);
            const downloadURL = await snapshot.ref.getDownloadURL();
            
            return downloadURL;
        }
    }
};

// Utility function to calculate distance between two points
function calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371; // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
}

// Export for use in other modules
if (typeof window !== 'undefined') {
    window.FirebaseAPI = FirebaseAPI;
    window.firebaseConfig = firebaseConfig;
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { FirebaseAPI, firebaseConfig };
}
