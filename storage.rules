rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isAdmin() {
      return request.auth.token.admin == true;
    }
    
    function isValidImageFile() {
      return request.resource.contentType.matches('image/.*') &&
             request.resource.size < 5 * 1024 * 1024; // 5MB limit
    }
    
    function isValidDocumentFile() {
      return request.resource.contentType in ['application/pdf', 'text/plain'] &&
             request.resource.size < 10 * 1024 * 1024; // 10MB limit
    }
    
    // Report images - organized by user and report
    match /reports/{userId}/{reportId}/{imageId} {
      // Anyone can read report images (for public viewing)
      allow read: if true;
      
      // Authenticated users can upload images for their own reports
      allow create: if isAuthenticated() && 
                       isOwner(userId) && 
                       isValidImageFile();
      
      // Users can update/delete their own report images
      allow update, delete: if isAuthenticated() && isOwner(userId);
      
      // <PERSON>mins can manage any report images
      allow create, update, delete: if isAuthenticated() && isAdmin();
    }
    
    // User profile images
    match /users/{userId}/profile/{imageId} {
      // Users can read their own profile images, others can read if public
      allow read: if true; // Profile images are generally public
      
      // Users can upload/update their own profile images
      allow create, update: if isAuthenticated() && 
                               isOwner(userId) && 
                               isValidImageFile();
      
      // Users can delete their own profile images
      allow delete: if isAuthenticated() && isOwner(userId);
      
      // Admins can manage any profile images
      allow create, update, delete: if isAuthenticated() && isAdmin();
    }
    
    // Temporary uploads (for processing before moving to final location)
    match /temp/{userId}/{fileName} {
      // Users can upload to their temp folder
      allow create: if isAuthenticated() && 
                       isOwner(userId) && 
                       (isValidImageFile() || isValidDocumentFile());
      
      // Users can read/delete their own temp files
      allow read, delete: if isAuthenticated() && isOwner(userId);
      
      // Admins can manage any temp files
      allow read, delete: if isAuthenticated() && isAdmin();
    }
    
    // System assets (logos, backgrounds, etc.)
    match /assets/{assetPath=**} {
      // Anyone can read system assets
      allow read: if true;
      
      // Only admins can manage system assets
      allow create, update, delete: if isAuthenticated() && isAdmin();
    }
    
    // Admin uploads
    match /admin/{adminPath=**} {
      // Only admins can access admin files
      allow read, create, update, delete: if isAuthenticated() && isAdmin();
    }
    
    // Backup files
    match /backups/{backupPath=**} {
      // Only admins can access backup files
      allow read, create, update, delete: if isAuthenticated() && isAdmin();
    }
    
    // Exported data
    match /exports/{userId}/{exportId} {
      // Users can read their own exported data
      allow read: if isAuthenticated() && isOwner(userId);
      
      // Only system can create exports (via Cloud Functions)
      allow create: if false; // Only server-side functions
      
      // Users can delete their own exports
      allow delete: if isAuthenticated() && isOwner(userId);
      
      // Admins can manage any exports
      allow read, delete: if isAuthenticated() && isAdmin();
    }
    
    // Logs and analytics files
    match /logs/{logPath=**} {
      // Only admins can access log files
      allow read: if isAuthenticated() && isAdmin();
      allow create, update, delete: if false; // Only server-side functions
    }
    
    // Default deny rule for any other paths
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
