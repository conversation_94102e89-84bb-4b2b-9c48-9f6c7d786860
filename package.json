{"name": "siana-baladi-pwa", "version": "1.0.0", "description": "Progressive Web App for reporting street and road issues in Arabic cities", "main": "index.html", "scripts": {"start": "npx http-server . -p 3000 -c-1", "dev": "npx live-server --port=3000 --host=localhost --entry-file=index.html", "build": "npm run minify-css && npm run minify-js", "minify-css": "npx cleancss -o assets/css/main.min.css assets/css/main.css assets/css/rtl.css", "minify-js": "npx terser assets/js/main.js assets/js/auth.js -o assets/js/app.min.js", "test": "npx jest", "lint": "npx eslint assets/js/**/*.js", "format": "npx prettier --write assets/js/**/*.js assets/css/**/*.css", "validate-html": "npx html-validate *.html pages/*.html", "lighthouse": "npx lighthouse http://localhost:3000 --output=html --output-path=./lighthouse-report.html", "pwa-audit": "npx pwa-asset-generator assets/images/app_logo.png assets/images/icons --manifest manifest.json", "deploy": "npm run build && npx firebase deploy"}, "keywords": ["pwa", "progressive-web-app", "arabic", "rtl", "street-maintenance", "city-reports", "firebase", "google-maps"], "author": "Siana Baladi Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/siana-baladi-pwa.git"}, "bugs": {"url": "https://github.com/your-username/siana-baladi-pwa/issues"}, "homepage": "https://siana-baladi.web.app", "devDependencies": {"clean-css-cli": "^5.6.2", "eslint": "^8.57.0", "html-validate": "^8.7.4", "http-server": "^14.1.1", "jest": "^29.7.0", "lighthouse": "^11.4.0", "live-server": "^1.2.2", "prettier": "^3.1.1", "pwa-asset-generator": "^6.3.1", "terser": "^5.26.0"}, "dependencies": {"firebase": "^10.7.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "pwa": {"name": "صيانة بلدي", "short_name": "صيانة بلدي", "description": "تطبيق للإبلاغ عن مشاكل الشوارع والحفر", "theme_color": "#2196F3", "background_color": "#ffffff", "display": "standalone", "orientation": "portrait-primary", "start_url": "/", "scope": "/", "lang": "ar", "dir": "rtl"}, "firebase": {"hosting": {"public": ".", "ignore": ["firebase.json", "**/.*", "**/node_modules/**", "**/*.md", "package*.json", "lighthouse-report.html"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "manifest.json", "headers": [{"key": "Cache-Control", "value": "max-age=0"}]}, {"source": "service-worker.js", "headers": [{"key": "Cache-Control", "value": "max-age=0"}]}]}}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "testMatch": ["<rootDir>/tests/**/*.test.js"], "collectCoverageFrom": ["assets/js/**/*.js", "!assets/js/**/*.min.js"]}, "eslintConfig": {"env": {"browser": true, "es2021": true, "serviceworker": true}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": 2021, "sourceType": "module"}, "globals": {"firebase": "readonly", "google": "readonly"}, "rules": {"no-unused-vars": "warn", "no-console": "off", "prefer-const": "error", "no-var": "error"}}, "prettier": {"semi": true, "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "printWidth": 100}}