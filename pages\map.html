<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="خريطة البلاغات - صيانة بلدي">
    <meta name="theme-color" content="#2196F3">
    
    <title>خريطة البلاغات - صيانة بلدي</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/rtl.css">
    <link rel="stylesheet" href="../assets/css/map.css">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Arabic Font -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="map-page">
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <button class="btn-icon back-btn" onclick="history.back()">
                    <span class="material-icons">arrow_forward</span>
                </button>
                <div class="header-title">
                    <h1>خريطة البلاغات</h1>
                </div>
                <div class="header-actions">
                    <button id="location-btn" class="btn-icon" title="موقعي">
                        <span class="material-icons">my_location</span>
                    </button>
                    <button id="layers-btn" class="btn-icon" title="طبقات الخريطة">
                        <span class="material-icons">layers</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Map Controls -->
    <div class="map-controls">
        <div class="container">
            <div class="controls-content">
                <!-- Filter Toggle -->
                <button id="map-filter-btn" class="control-btn">
                    <span class="material-icons">tune</span>
                    <span>فلترة</span>
                </button>
                
                <!-- Legend Toggle -->
                <button id="legend-btn" class="control-btn">
                    <span class="material-icons">info</span>
                    <span>دليل الألوان</span>
                </button>
                
                <!-- View Toggle -->
                <div class="view-toggle">
                    <button class="view-btn active" data-view="satellite">
                        <span class="material-icons">satellite</span>
                    </button>
                    <button class="view-btn" data-view="roadmap">
                        <span class="material-icons">map</span>
                    </button>
                    <button class="view-btn" data-view="terrain">
                        <span class="material-icons">terrain</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Map Filter Panel -->
    <div id="map-filter-panel" class="filter-panel hidden">
        <div class="filter-content">
            <h3>فلترة البلاغات</h3>
            <div class="filter-group">
                <label>الحالة</label>
                <div class="filter-checkboxes">
                    <label class="checkbox-item">
                        <input type="checkbox" value="pending" checked>
                        <span class="checkmark pending"></span>
                        قيد المراجعة
                    </label>
                    <label class="checkbox-item">
                        <input type="checkbox" value="in-progress" checked>
                        <span class="checkmark in-progress"></span>
                        قيد التنفيذ
                    </label>
                    <label class="checkbox-item">
                        <input type="checkbox" value="completed" checked>
                        <span class="checkmark completed"></span>
                        مكتمل
                    </label>
                </div>
            </div>
            <div class="filter-group">
                <label>نوع المشكلة</label>
                <div class="filter-checkboxes">
                    <label class="checkbox-item">
                        <input type="checkbox" value="pothole" checked>
                        <span class="checkmark pothole"></span>
                        حفرة في الطريق
                    </label>
                    <label class="checkbox-item">
                        <input type="checkbox" value="lighting" checked>
                        <span class="checkmark lighting"></span>
                        إضاءة الشارع
                    </label>
                    <label class="checkbox-item">
                        <input type="checkbox" value="drainage" checked>
                        <span class="checkmark drainage"></span>
                        تصريف المياه
                    </label>
                </div>
            </div>
            <div class="filter-actions">
                <button class="btn btn-primary" id="apply-map-filters">تطبيق</button>
                <button class="btn btn-secondary" id="clear-map-filters">مسح</button>
            </div>
        </div>
    </div>

    <!-- Legend Panel -->
    <div id="legend-panel" class="legend-panel hidden">
        <div class="legend-content">
            <h3>دليل الألوان</h3>
            <div class="legend-items">
                <div class="legend-item">
                    <div class="legend-marker pending"></div>
                    <span>قيد المراجعة</span>
                </div>
                <div class="legend-item">
                    <div class="legend-marker in-progress"></div>
                    <span>قيد التنفيذ</span>
                </div>
                <div class="legend-item">
                    <div class="legend-marker completed"></div>
                    <span>مكتمل</span>
                </div>
                <div class="legend-item">
                    <div class="legend-marker rejected"></div>
                    <span>مرفوض</span>
                </div>
            </div>
            <div class="legend-types">
                <h4>أنواع المشاكل</h4>
                <div class="type-items">
                    <div class="type-item">
                        <span class="material-icons">construction</span>
                        <span>حفرة في الطريق</span>
                    </div>
                    <div class="type-item">
                        <span class="material-icons">lightbulb</span>
                        <span>إضاءة الشارع</span>
                    </div>
                    <div class="type-item">
                        <span class="material-icons">water_drop</span>
                        <span>تصريف المياه</span>
                    </div>
                    <div class="type-item">
                        <span class="material-icons">directions_walk</span>
                        <span>الرصيف</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Map Container -->
    <main class="main-content">
        <div id="map-container" class="map-container">
            <div id="google-map" class="google-map">
                <!-- Google Map will be loaded here -->
            </div>
            
            <!-- Map Loading -->
            <div id="map-loading" class="map-loading">
                <div class="loading-content">
                    <div class="spinner"></div>
                    <p>جاري تحميل الخريطة...</p>
                </div>
            </div>
            
            <!-- Map Error -->
            <div id="map-error" class="map-error hidden">
                <div class="error-content">
                    <span class="material-icons">error</span>
                    <h3>خطأ في تحميل الخريطة</h3>
                    <p>تعذر تحميل الخريطة. تأكد من اتصالك بالإنترنت.</p>
                    <button class="btn btn-primary" onclick="location.reload()">إعادة المحاولة</button>
                </div>
            </div>
        </div>
    </main>

    <!-- Report Info Panel -->
    <div id="report-info-panel" class="report-info-panel hidden">
        <div class="report-info-content">
            <div class="report-info-header">
                <h3 id="report-info-title">تفاصيل البلاغ</h3>
                <button id="close-report-info" class="btn-icon">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="report-info-body" id="report-info-body">
                <!-- Report details will be loaded here -->
            </div>
            <div class="report-info-actions">
                <button class="btn btn-primary" id="view-full-report">عرض التفاصيل الكاملة</button>
            </div>
        </div>
    </div>

    <!-- Floating Action Button -->
    <button class="fab" onclick="window.location.href='new-report.html'" title="إنشاء بلاغ جديد">
        <span class="material-icons">add</span>
    </button>

    <!-- Map Statistics -->
    <div class="map-stats">
        <div class="stat-item">
            <span class="stat-number" id="visible-reports">0</span>
            <span class="stat-label">بلاغ مرئي</span>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/auth.js"></script>
    <script src="../assets/js/map.js"></script>
    <script src="../config/firebase-config.js"></script>
    
    <!-- Google Maps API -->
    <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap&libraries=places&language=ar&region=SA">
    </script>
</body>
</html>
