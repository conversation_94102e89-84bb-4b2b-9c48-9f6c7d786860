/* RTL (Right-to-Left) Specific Styles for Arabic */

/* Base RTL Setup */
[dir="rtl"] {
    direction: rtl;
    text-align: right;
}

[dir="rtl"] body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* RTL Typography */
[dir="rtl"] h1,
[dir="rtl"] h2,
[dir="rtl"] h3,
[dir="rtl"] h4,
[dir="rtl"] h5,
[dir="rtl"] h6 {
    font-weight: 600;
    line-height: 1.3;
}

/* RTL Button Adjustments */
[dir="rtl"] .btn {
    flex-direction: row-reverse;
}

[dir="rtl"] .btn .material-icons {
    margin-right: 0;
    margin-left: var(--spacing-xs);
}

/* RTL Header Adjustments */
[dir="rtl"] .header-content {
    flex-direction: row-reverse;
}

[dir="rtl"] .logo {
    margin-left: 0;
    margin-right: var(--spacing-sm);
}

[dir="rtl"] .header-actions {
    flex-direction: row-reverse;
}

/* RTL Hero Section */
[dir="rtl"] .hero {
    grid-template-columns: 1fr 1fr;
}

[dir="rtl"] .hero-actions {
    flex-direction: row-reverse;
    justify-content: flex-start;
}

/* RTL Stats Cards */
[dir="rtl"] .stat-card {
    flex-direction: row-reverse;
}

[dir="rtl"] .stat-icon {
    margin-left: 0;
    margin-right: var(--spacing-md);
}

/* RTL Features Grid */
[dir="rtl"] .feature-card {
    text-align: center;
}

/* RTL Footer */
[dir="rtl"] .footer-content {
    text-align: right;
}

[dir="rtl"] .footer-section ul {
    padding-right: 0;
}

/* RTL Form Elements */
[dir="rtl"] input,
[dir="rtl"] textarea,
[dir="rtl"] select {
    text-align: right;
    padding-right: var(--spacing-md);
    padding-left: var(--spacing-sm);
}

[dir="rtl"] .form-group label {
    text-align: right;
}

/* RTL Navigation */
[dir="rtl"] .nav-menu {
    flex-direction: row-reverse;
}

[dir="rtl"] .nav-item {
    margin-left: 0;
    margin-right: var(--spacing-md);
}

/* RTL Cards and Lists */
[dir="rtl"] .card {
    text-align: right;
}

[dir="rtl"] .list-item {
    flex-direction: row-reverse;
}

[dir="rtl"] .list-item .icon {
    margin-left: 0;
    margin-right: var(--spacing-sm);
}

/* RTL Modal and Dialog */
[dir="rtl"] .modal-header {
    flex-direction: row-reverse;
}

[dir="rtl"] .modal-close {
    left: var(--spacing-md);
    right: auto;
}

/* RTL Breadcrumbs */
[dir="rtl"] .breadcrumb {
    flex-direction: row-reverse;
}

[dir="rtl"] .breadcrumb-separator::before {
    content: "\\";
    transform: scaleX(-1);
}

/* RTL Tabs */
[dir="rtl"] .tabs {
    flex-direction: row-reverse;
}

[dir="rtl"] .tab {
    margin-left: 0;
    margin-right: var(--spacing-sm);
}

/* RTL Dropdown */
[dir="rtl"] .dropdown-menu {
    right: 0;
    left: auto;
    text-align: right;
}

/* RTL Tooltips */
[dir="rtl"] .tooltip {
    text-align: right;
}

[dir="rtl"] .tooltip-arrow {
    transform: scaleX(-1);
}

/* RTL Progress Bars */
[dir="rtl"] .progress-bar {
    transform: scaleX(-1);
}

[dir="rtl"] .progress-bar .progress-fill {
    transform: scaleX(-1);
}

/* RTL Animations for RTL */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

[dir="rtl"] .slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

[dir="rtl"] .slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

/* RTL Specific Utilities */
[dir="rtl"] .text-left {
    text-align: right !important;
}

[dir="rtl"] .text-right {
    text-align: left !important;
}

[dir="rtl"] .float-left {
    float: right !important;
}

[dir="rtl"] .float-right {
    float: left !important;
}

[dir="rtl"] .mr-1 { margin-left: 0.25rem !important; margin-right: 0 !important; }
[dir="rtl"] .mr-2 { margin-left: 0.5rem !important; margin-right: 0 !important; }
[dir="rtl"] .mr-3 { margin-left: 1rem !important; margin-right: 0 !important; }
[dir="rtl"] .mr-4 { margin-left: 1.5rem !important; margin-right: 0 !important; }

[dir="rtl"] .ml-1 { margin-right: 0.25rem !important; margin-left: 0 !important; }
[dir="rtl"] .ml-2 { margin-right: 0.5rem !important; margin-left: 0 !important; }
[dir="rtl"] .ml-3 { margin-right: 1rem !important; margin-left: 0 !important; }
[dir="rtl"] .ml-4 { margin-right: 1.5rem !important; margin-left: 0 !important; }

[dir="rtl"] .pr-1 { padding-left: 0.25rem !important; padding-right: 0 !important; }
[dir="rtl"] .pr-2 { padding-left: 0.5rem !important; padding-right: 0 !important; }
[dir="rtl"] .pr-3 { padding-left: 1rem !important; padding-right: 0 !important; }
[dir="rtl"] .pr-4 { padding-left: 1.5rem !important; padding-right: 0 !important; }

[dir="rtl"] .pl-1 { padding-right: 0.25rem !important; padding-left: 0 !important; }
[dir="rtl"] .pl-2 { padding-right: 0.5rem !important; padding-left: 0 !important; }
[dir="rtl"] .pl-3 { padding-right: 1rem !important; padding-left: 0 !important; }
[dir="rtl"] .pl-4 { padding-right: 1.5rem !important; padding-left: 0 !important; }

/* RTL Border Radius Adjustments */
[dir="rtl"] .rounded-left {
    border-top-right-radius: var(--border-radius-md) !important;
    border-bottom-right-radius: var(--border-radius-md) !important;
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
}

[dir="rtl"] .rounded-right {
    border-top-left-radius: var(--border-radius-md) !important;
    border-bottom-left-radius: var(--border-radius-md) !important;
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
}

/* RTL Mobile Responsive Adjustments */
@media (max-width: 768px) {
    [dir="rtl"] .hero-actions {
        justify-content: center;
        flex-direction: column;
    }
    
    [dir="rtl"] .header-content {
        flex-direction: column;
        text-align: center;
    }
    
    [dir="rtl"] .header-actions {
        flex-direction: row;
        justify-content: center;
    }
}

/* Language Toggle Specific */
[dir="rtl"] .language-toggle {
    position: relative;
}

[dir="rtl"] .language-toggle::after {
    content: "EN";
    font-size: var(--font-size-xs);
    position: absolute;
    bottom: -2px;
    right: -2px;
    background: var(--primary-color);
    color: white;
    padding: 1px 3px;
    border-radius: 2px;
    font-weight: bold;
}

/* RTL Print Styles */
@media print {
    [dir="rtl"] body {
        direction: rtl;
        text-align: right;
    }
    
    [dir="rtl"] .no-print {
        display: none !important;
    }
}
