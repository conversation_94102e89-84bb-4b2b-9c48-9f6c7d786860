{"hosting": {"public": ".", "ignore": ["firebase.json", "**/.*", "**/node_modules/**", "**/*.md", "package*.json", "lighthouse-report.html", "tests/**", ".github/**", "SETUP.md", "CONTRIBUTING.md"], "rewrites": [{"source": "/pages/**", "destination": "/pages/index.html"}, {"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000, immutable"}]}, {"source": "**/*.@(png|jpg|jpeg|gif|svg|webp|ico)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000, immutable"}]}, {"source": "manifest.json", "headers": [{"key": "Cache-Control", "value": "max-age=0, no-cache, no-store, must-revalidate"}, {"key": "Content-Type", "value": "application/manifest+json; charset=utf-8"}]}, {"source": "service-worker.js", "headers": [{"key": "Cache-Control", "value": "max-age=0, no-cache, no-store, must-revalidate"}, {"key": "Content-Type", "value": "application/javascript; charset=utf-8"}]}, {"source": "**/*.html", "headers": [{"key": "Cache-Control", "value": "max-age=3600"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' https://www.gstatic.com https://maps.googleapis.com https://www.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://*.googleapis.com https://*.firebaseio.com https://*.cloudfunctions.net wss://*.firebaseio.com; frame-src 'none'; object-src 'none';"}]}, {"source": "**", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "cleanUrls": true, "trailingSlash": false}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "functions": {"predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}, "emulators": {"auth": {"port": 9099}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "storage": {"port": 9199}, "ui": {"enabled": true, "port": 4000}, "singleProjectMode": true}}