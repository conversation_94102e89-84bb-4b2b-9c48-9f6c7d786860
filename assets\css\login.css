/* Login Page Specific Styles */

.login-page {
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
    min-height: 100vh;
}

.login-page .header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.login-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxl);
    align-items: center;
    min-height: calc(100vh - 80px);
    padding: var(--spacing-xl) 0;
}

.login-card {
    background: var(--background-card);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-xxl);
    max-width: 500px;
    width: 100%;
    margin: 0 auto;
}

.login-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.login-logo img {
    width: 80px;
    height: 80px;
    margin-bottom: var(--spacing-md);
}

.login-header h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.login-header p {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
}

.login-methods {
    margin-bottom: var(--spacing-lg);
}

.login-method {
    margin-bottom: var(--spacing-lg);
}

.login-method h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.login-form {
    margin-bottom: var(--spacing-md);
}

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.phone-input {
    display: flex;
    gap: var(--spacing-xs);
}

.country-code {
    flex: 0 0 120px;
    padding: var(--spacing-sm) var(--spacing-xs);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-family: inherit;
    font-size: var(--font-size-base);
    background: var(--background-primary);
    color: var(--text-primary);
    transition: border-color var(--transition-fast);
}

.country-code:focus {
    outline: none;
    border-color: var(--primary-color);
}

#phone-number {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    font-family: inherit;
    font-size: var(--font-size-base);
    background: var(--background-primary);
    color: var(--text-primary);
    transition: border-color var(--transition-fast);
    text-align: right;
}

#phone-number:focus {
    outline: none;
    border-color: var(--primary-color);
}

#phone-number::placeholder {
    color: var(--text-disabled);
    text-align: right;
}

/* OTP Section */
.otp-section {
    margin-top: var(--spacing-lg);
}

.otp-section.hidden {
    display: none;
}

.otp-section h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.otp-section p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

#sent-phone {
    font-weight: 600;
    color: var(--primary-color);
}

.otp-inputs {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: center;
    margin-bottom: var(--spacing-lg);
}

.otp-digit {
    width: 50px;
    height: 50px;
    text-align: center;
    font-size: var(--font-size-xl);
    font-weight: 600;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background: var(--background-primary);
    color: var(--text-primary);
    transition: all var(--transition-fast);
}

.otp-digit:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.otp-digit.filled {
    background: var(--primary-light);
    border-color: var(--primary-color);
}

.otp-actions {
    display: flex;
    justify-content: space-between;
    margin-top: var(--spacing-md);
}

.btn-link {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: var(--font-size-sm);
    text-decoration: underline;
    cursor: pointer;
    transition: color var(--transition-fast);
}

.btn-link:hover {
    color: var(--primary-dark);
}

/* Login Divider */
.login-divider {
    position: relative;
    text-align: center;
    margin: var(--spacing-xl) 0;
}

.login-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
}

.login-divider span {
    background: var(--background-card);
    padding: 0 var(--spacing-md);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Google Login Button */
.btn-google {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    background: var(--background-primary);
    color: var(--text-primary);
    font-family: inherit;
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    margin-bottom: var(--spacing-md);
}

.btn-google:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.google-icon {
    width: 20px;
    height: 20px;
}

/* Login Footer */
.login-footer {
    text-align: center;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.5;
}

.login-footer .link {
    color: var(--primary-color);
    text-decoration: none;
}

.login-footer .link:hover {
    text-decoration: underline;
}

/* Background Image */
.login-background {
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-background img {
    width: 100%;
    max-width: 500px;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-normal);
}

.loading-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-content {
    background: var(--background-card);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    text-align: center;
    box-shadow: var(--shadow-xl);
}

.loading-content .spinner {
    margin: 0 auto var(--spacing-md);
}

.loading-content p {
    color: var(--text-primary);
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        padding: var(--spacing-md) 0;
    }
    
    .login-card {
        padding: var(--spacing-lg);
        margin: 0 var(--spacing-sm);
    }
    
    .login-background {
        order: -1;
    }
    
    .login-background img {
        max-width: 300px;
    }
    
    .otp-inputs {
        gap: var(--spacing-xs);
    }
    
    .otp-digit {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-lg);
    }
}

@media (max-width: 480px) {
    .login-card {
        padding: var(--spacing-md);
        margin: 0;
        border-radius: 0;
    }
    
    .phone-input {
        flex-direction: column;
    }
    
    .country-code {
        flex: none;
    }
    
    .otp-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }
}

/* Animation for form transitions */
.login-method {
    transition: all var(--transition-normal);
}

.login-method.slide-out {
    transform: translateX(-100%);
    opacity: 0;
}

.login-method.slide-in {
    transform: translateX(0);
    opacity: 1;
}

/* Success state */
.form-success {
    color: var(--success-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* Error state */
.form-error {
    color: var(--error-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.form-group.error input,
.form-group.error select {
    border-color: var(--error-color);
}

.form-group.success input,
.form-group.success select {
    border-color: var(--success-color);
}
