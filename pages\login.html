<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="تسجيل الدخول - صيانة بلدي">
    <meta name="theme-color" content="#2196F3">
    
    <title>تسجيل الدخول - صيانة بلدي</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/rtl.css">
    <link rel="stylesheet" href="../assets/css/login.css">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Arabic Font -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body class="login-page">
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <button class="btn-icon back-btn" onclick="history.back()">
                    <span class="material-icons">arrow_forward</span>
                </button>
                <div class="header-title">
                    <img src="../assets/images/app_logo.png" alt="صيانة بلدي" class="logo">
                    <h1>تسجيل الدخول</h1>
                </div>
                <button id="language-toggle" class="btn-icon" title="تغيير اللغة">
                    <span class="material-icons">language</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="login-container">
                <!-- Login Form Card -->
                <div class="login-card">
                    <div class="login-header">
                        <div class="login-logo">
                            <img src="../assets/images/app_logo.png" alt="صيانة بلدي">
                        </div>
                        <h2>مرحباً بك</h2>
                        <p>سجل دخولك للوصول إلى جميع الميزات</p>
                    </div>

                    <!-- Login Methods -->
                    <div class="login-methods">
                        <!-- Phone Login -->
                        <div class="login-method active" id="phone-login">
                            <h3>تسجيل الدخول برقم الهاتف</h3>
                            <form id="phone-login-form" class="login-form">
                                <div class="form-group">
                                    <label for="phone-number">رقم الهاتف</label>
                                    <div class="phone-input">
                                        <select id="country-code" class="country-code">
                                            <option value="+966">🇸🇦 +966</option>
                                            <option value="+971">🇦🇪 +971</option>
                                            <option value="+965">🇰🇼 +965</option>
                                            <option value="+973">🇧🇭 +973</option>
                                            <option value="+974">🇶🇦 +974</option>
                                        </select>
                                        <input type="tel" id="phone-number" placeholder="5xxxxxxxx" required>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary btn-large" id="send-otp-btn">
                                    <span class="material-icons">sms</span>
                                    إرسال رمز التحقق
                                </button>
                            </form>

                            <!-- OTP Verification -->
                            <div id="otp-verification" class="otp-section hidden">
                                <h3>أدخل رمز التحقق</h3>
                                <p>تم إرسال رمز التحقق إلى <span id="sent-phone"></span></p>
                                <form id="otp-form" class="otp-form">
                                    <div class="otp-inputs">
                                        <input type="text" maxlength="1" class="otp-digit" data-index="0">
                                        <input type="text" maxlength="1" class="otp-digit" data-index="1">
                                        <input type="text" maxlength="1" class="otp-digit" data-index="2">
                                        <input type="text" maxlength="1" class="otp-digit" data-index="3">
                                        <input type="text" maxlength="1" class="otp-digit" data-index="4">
                                        <input type="text" maxlength="1" class="otp-digit" data-index="5">
                                    </div>
                                    <button type="submit" class="btn btn-primary btn-large" id="verify-otp-btn">
                                        <span class="material-icons">verified</span>
                                        تحقق من الرمز
                                    </button>
                                </form>
                                <div class="otp-actions">
                                    <button class="btn-link" id="resend-otp">إعادة إرسال الرمز</button>
                                    <button class="btn-link" id="change-phone">تغيير رقم الهاتف</button>
                                </div>
                            </div>
                        </div>

                        <!-- Divider -->
                        <div class="login-divider">
                            <span>أو</span>
                        </div>

                        <!-- Google Login -->
                        <button class="btn btn-google" id="google-login-btn">
                            <svg class="google-icon" viewBox="0 0 24 24">
                                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                            </svg>
                            تسجيل الدخول بـ Google
                        </button>

                        <!-- Guest Login -->
                        <button class="btn btn-secondary" id="guest-login-btn">
                            <span class="material-icons">visibility</span>
                            متابعة كزائر
                        </button>
                    </div>

                    <!-- Terms and Privacy -->
                    <div class="login-footer">
                        <p>بالمتابعة، أنت توافق على <a href="#" class="link">شروط الاستخدام</a> و <a href="#" class="link">سياسة الخصوصية</a></p>
                    </div>
                </div>

                <!-- Background Image -->
                <div class="login-background">
                    <img src="../assets/images/login_background.png" alt="خلفية تسجيل الدخول">
                </div>
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading-content">
            <div class="spinner"></div>
            <p id="loading-text">جاري تسجيل الدخول...</p>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/auth.js"></script>
    <script src="../assets/js/login.js"></script>
    <script src="../config/firebase-config.js"></script>
</body>
</html>
