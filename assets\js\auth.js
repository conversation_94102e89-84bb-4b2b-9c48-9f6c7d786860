// Authentication module for Siana Baladi PWA
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.isGuest = false;
        this.init();
    }

    init() {
        this.checkAuthState();
        this.setupAuthListeners();
    }

    checkAuthState() {
        // Check if user is logged in from localStorage
        const savedUser = localStorage.getItem('currentUser');
        const isGuest = localStorage.getItem('isGuest') === 'true';
        
        if (savedUser) {
            this.currentUser = JSON.parse(savedUser);
            this.updateUIForLoggedInUser();
        } else if (isGuest) {
            this.isGuest = true;
            this.updateUIForGuest();
        } else {
            this.updateUIForLoggedOutUser();
        }
    }

    setupAuthListeners() {
        // Listen for auth state changes from Firebase
        // This would integrate with Firebase Auth
        document.addEventListener('authStateChanged', (event) => {
            this.handleAuthStateChange(event.detail);
        });
    }

    handleAuthStateChange(user) {
        if (user) {
            this.currentUser = user;
            this.isGuest = false;
            localStorage.setItem('currentUser', JSON.stringify(user));
            localStorage.removeItem('isGuest');
            this.updateUIForLoggedInUser();
        } else {
            this.currentUser = null;
            this.isGuest = false;
            localStorage.removeItem('currentUser');
            localStorage.removeItem('isGuest');
            this.updateUIForLoggedOutUser();
        }
    }

    updateUIForLoggedInUser() {
        const loginBtn = document.getElementById('login-btn');
        if (loginBtn) {
            loginBtn.textContent = this.currentUser.displayName || this.currentUser.phoneNumber || 'المستخدم';
            loginBtn.onclick = () => this.showUserMenu();
        }

        // Show user-specific content
        this.showAuthenticatedContent();
    }

    updateUIForGuest() {
        const loginBtn = document.getElementById('login-btn');
        if (loginBtn) {
            loginBtn.textContent = 'زائر';
            loginBtn.onclick = () => this.showGuestMenu();
        }

        // Show limited content for guests
        this.showGuestContent();
    }

    updateUIForLoggedOutUser() {
        const loginBtn = document.getElementById('login-btn');
        if (loginBtn) {
            loginBtn.textContent = 'تسجيل الدخول';
            loginBtn.onclick = () => this.navigateToLogin();
        }

        // Hide authenticated content
        this.hideAuthenticatedContent();
    }

    showUserMenu() {
        const menu = this.createUserMenu();
        document.body.appendChild(menu);
    }

    showGuestMenu() {
        const menu = this.createGuestMenu();
        document.body.appendChild(menu);
    }

    createUserMenu() {
        const menu = document.createElement('div');
        menu.className = 'user-menu-overlay';
        menu.innerHTML = `
            <div class="user-menu">
                <div class="user-menu-header">
                    <img src="${this.currentUser.photoURL || 'assets/images/default-avatar.png'}" alt="صورة المستخدم" class="user-avatar">
                    <div class="user-info">
                        <h3>${this.currentUser.displayName || 'المستخدم'}</h3>
                        <p>${this.currentUser.phoneNumber || this.currentUser.email || ''}</p>
                    </div>
                </div>
                <div class="user-menu-items">
                    <button class="menu-item" onclick="this.navigateToProfile()">
                        <span class="material-icons">person</span>
                        الملف الشخصي
                    </button>
                    <button class="menu-item" onclick="this.navigateToMyReports()">
                        <span class="material-icons">list_alt</span>
                        بلاغاتي
                    </button>
                    <button class="menu-item" onclick="this.navigateToSettings()">
                        <span class="material-icons">settings</span>
                        الإعدادات
                    </button>
                    <button class="menu-item logout" onclick="authManager.logout()">
                        <span class="material-icons">logout</span>
                        تسجيل الخروج
                    </button>
                </div>
                <button class="menu-close" onclick="this.closeUserMenu()">
                    <span class="material-icons">close</span>
                </button>
            </div>
        `;

        // Close menu when clicking outside
        menu.addEventListener('click', (e) => {
            if (e.target === menu) {
                this.closeUserMenu();
            }
        });

        return menu;
    }

    createGuestMenu() {
        const menu = document.createElement('div');
        menu.className = 'user-menu-overlay';
        menu.innerHTML = `
            <div class="user-menu">
                <div class="user-menu-header">
                    <div class="guest-icon">
                        <span class="material-icons">person_outline</span>
                    </div>
                    <div class="user-info">
                        <h3>زائر</h3>
                        <p>تصفح محدود</p>
                    </div>
                </div>
                <div class="user-menu-items">
                    <button class="menu-item" onclick="authManager.navigateToLogin()">
                        <span class="material-icons">login</span>
                        تسجيل الدخول
                    </button>
                    <button class="menu-item" onclick="authManager.navigateToRegister()">
                        <span class="material-icons">person_add</span>
                        إنشاء حساب
                    </button>
                    <button class="menu-item" onclick="this.continueAsGuest()">
                        <span class="material-icons">visibility</span>
                        متابعة كزائر
                    </button>
                </div>
                <button class="menu-close" onclick="this.closeUserMenu()">
                    <span class="material-icons">close</span>
                </button>
            </div>
        `;

        // Close menu when clicking outside
        menu.addEventListener('click', (e) => {
            if (e.target === menu) {
                this.closeUserMenu();
            }
        });

        return menu;
    }

    closeUserMenu() {
        const menu = document.querySelector('.user-menu-overlay');
        if (menu) {
            menu.remove();
        }
    }

    showAuthenticatedContent() {
        // Show elements that require authentication
        document.querySelectorAll('.auth-required').forEach(el => {
            el.style.display = 'block';
        });

        // Hide guest-only content
        document.querySelectorAll('.guest-only').forEach(el => {
            el.style.display = 'none';
        });
    }

    showGuestContent() {
        // Show limited content for guests
        document.querySelectorAll('.guest-allowed').forEach(el => {
            el.style.display = 'block';
        });

        // Hide authenticated-only content
        document.querySelectorAll('.auth-only').forEach(el => {
            el.style.display = 'none';
        });
    }

    hideAuthenticatedContent() {
        // Hide elements that require authentication
        document.querySelectorAll('.auth-required').forEach(el => {
            el.style.display = 'none';
        });

        // Show login prompts
        document.querySelectorAll('.login-prompt').forEach(el => {
            el.style.display = 'block';
        });
    }

    async loginWithPhone(phoneNumber) {
        try {
            // This would integrate with Firebase Auth
            const result = await this.sendOTP(phoneNumber);
            return result;
        } catch (error) {
            console.error('Phone login error:', error);
            throw error;
        }
    }

    async verifyOTP(phoneNumber, otp) {
        try {
            // This would integrate with Firebase Auth
            const user = await this.verifyPhoneOTP(phoneNumber, otp);
            this.handleAuthStateChange(user);
            return user;
        } catch (error) {
            console.error('OTP verification error:', error);
            throw error;
        }
    }

    async loginWithGoogle() {
        try {
            // This would integrate with Firebase Auth
            const user = await this.signInWithGoogle();
            this.handleAuthStateChange(user);
            return user;
        } catch (error) {
            console.error('Google login error:', error);
            throw error;
        }
    }

    continueAsGuest() {
        this.isGuest = true;
        localStorage.setItem('isGuest', 'true');
        this.updateUIForGuest();
        this.closeUserMenu();
        
        // Show notification
        if (window.sianaBaladi) {
            window.sianaBaladi.showNotification('تم الدخول كزائر - الميزات محدودة', 'info');
        }
    }

    async logout() {
        try {
            // This would integrate with Firebase Auth
            await this.signOut();
            this.handleAuthStateChange(null);
            
            // Show notification
            if (window.sianaBaladi) {
                window.sianaBaladi.showNotification('تم تسجيل الخروج بنجاح', 'success');
            }
            
            this.closeUserMenu();
        } catch (error) {
            console.error('Logout error:', error);
            if (window.sianaBaladi) {
                window.sianaBaladi.showNotification('حدث خطأ أثناء تسجيل الخروج', 'error');
            }
        }
    }

    // Mock Firebase methods - would be replaced with actual Firebase integration
    async sendOTP(phoneNumber) {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({ verificationId: 'mock-verification-id' });
            }, 1000);
        });
    }

    async verifyPhoneOTP(phoneNumber, otp) {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                if (otp === '123456') { // Mock OTP
                    resolve({
                        uid: 'mock-uid',
                        phoneNumber: phoneNumber,
                        displayName: null,
                        photoURL: null
                    });
                } else {
                    reject(new Error('Invalid OTP'));
                }
            }, 1000);
        });
    }

    async signInWithGoogle() {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    uid: 'mock-google-uid',
                    email: '<EMAIL>',
                    displayName: 'مستخدم تجريبي',
                    photoURL: 'https://via.placeholder.com/100'
                });
            }, 1000);
        });
    }

    async signOut() {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve();
            }, 500);
        });
    }

    // Navigation methods
    navigateToLogin() {
        window.location.href = 'pages/login.html';
    }

    navigateToRegister() {
        window.location.href = 'pages/register.html';
    }

    navigateToProfile() {
        window.location.href = 'pages/profile.html';
    }

    navigateToMyReports() {
        window.location.href = 'pages/my-reports.html';
    }

    navigateToSettings() {
        window.location.href = 'pages/settings.html';
    }

    // Utility methods
    isAuthenticated() {
        return this.currentUser !== null;
    }

    isGuestUser() {
        return this.isGuest;
    }

    getCurrentUser() {
        return this.currentUser;
    }

    requireAuth() {
        if (!this.isAuthenticated() && !this.isGuestUser()) {
            this.navigateToLogin();
            return false;
        }
        return true;
    }
}

// Initialize auth manager
document.addEventListener('DOMContentLoaded', () => {
    window.authManager = new AuthManager();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}
