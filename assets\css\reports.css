/* Reports Page Specific Styles */

.reports-page {
    background: var(--background-secondary);
}

/* Search Bar */
.search-bar {
    background: var(--background-primary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md) 0;
    transition: all var(--transition-normal);
}

.search-bar.hidden {
    transform: translateY(-100%);
    opacity: 0;
}

.search-input-container {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--background-secondary);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-sm) var(--spacing-md);
    transition: border-color var(--transition-fast);
}

.search-input-container:focus-within {
    border-color: var(--primary-color);
}

.search-input-container .material-icons {
    color: var(--text-secondary);
    margin-left: var(--spacing-sm);
}

#search-input {
    flex: 1;
    border: none;
    background: transparent;
    font-family: inherit;
    font-size: var(--font-size-base);
    color: var(--text-primary);
    text-align: right;
}

#search-input:focus {
    outline: none;
}

#search-input::placeholder {
    color: var(--text-disabled);
    text-align: right;
}

/* Filter Panel */
.filter-panel {
    background: var(--background-primary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-lg) 0;
    transition: all var(--transition-normal);
}

.filter-panel.hidden {
    transform: translateY(-100%);
    opacity: 0;
    max-height: 0;
    padding: 0;
    overflow: hidden;
}

.filter-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    align-items: start;
}

.filter-group {
    margin-bottom: var(--spacing-md);
}

.filter-group label {
    display: block;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.filter-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.filter-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: background-color var(--transition-fast);
}

.filter-option:hover {
    background: var(--background-secondary);
}

.filter-option input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    position: relative;
    transition: all var(--transition-fast);
}

.filter-option input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.filter-option input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: var(--font-size-sm);
    font-weight: bold;
}

.filter-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
    grid-column: 1 / -1;
}

/* Reports Summary */
.reports-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--background-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.summary-stats {
    display: flex;
    gap: var(--spacing-lg);
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.view-toggle {
    display: flex;
    gap: var(--spacing-xs);
    background: var(--background-secondary);
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-md);
}

.view-btn {
    padding: var(--spacing-sm);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.view-btn.active,
.view-btn:hover {
    background: var(--primary-color);
    color: white;
}

/* Reports Container */
.reports-container {
    min-height: 400px;
    position: relative;
}

.reports-list {
    display: grid;
    gap: var(--spacing-md);
}

/* Report Card */
.report-card {
    background: var(--background-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    padding: var(--spacing-lg);
    transition: all var(--transition-fast);
    cursor: pointer;
    border-right: 4px solid transparent;
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.report-card.pending {
    border-right-color: var(--warning-color);
}

.report-card.in-progress {
    border-right-color: var(--info-color);
}

.report-card.completed {
    border-right-color: var(--success-color);
}

.report-card.rejected {
    border-right-color: var(--error-color);
}

.report-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.report-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.report-id {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-family: monospace;
}

.report-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.report-status.pending {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.report-status.in-progress {
    background: rgba(0, 188, 212, 0.1);
    color: var(--info-color);
}

.report-status.completed {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.report-status.rejected {
    background: rgba(244, 67, 54, 0.1);
    color: var(--error-color);
}

.report-content {
    margin-bottom: var(--spacing-md);
}

.report-description {
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: var(--spacing-sm);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.report-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.report-location {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.report-date {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.report-images {
    display: flex;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-sm);
}

.report-image {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-md);
    object-fit: cover;
    border: 2px solid var(--border-color);
}

/* Grid View */
.reports-list.grid-view {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
}

.reports-list.grid-view .report-card {
    height: 100%;
}

/* Loading Indicator */
.loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xxl);
    color: var(--text-secondary);
}

.loading-indicator.hidden {
    display: none;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--spacing-xxl);
    color: var(--text-secondary);
}

.empty-state.hidden {
    display: none;
}

.empty-icon {
    font-size: 4rem;
    color: var(--text-disabled);
    margin-bottom: var(--spacing-lg);
}

.empty-state h3 {
    font-size: var(--font-size-xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.empty-state p {
    margin-bottom: var(--spacing-lg);
}

/* Load More Button */
.load-more-container {
    text-align: center;
    margin-top: var(--spacing-lg);
}

#load-more-btn.hidden {
    display: none;
}

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: var(--spacing-lg);
    left: var(--spacing-lg);
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    border: none;
    box-shadow: var(--shadow-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    z-index: 1000;
}

.fab:hover {
    background: var(--primary-dark);
    transform: scale(1.1);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-normal);
}

.modal.hidden {
    opacity: 0;
    pointer-events: none;
}

.modal-content {
    background: var(--background-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--background-secondary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
    .reports-summary {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
    
    .summary-stats {
        justify-content: center;
    }
    
    .filter-content {
        grid-template-columns: 1fr;
    }
    
    .filter-actions {
        justify-content: center;
    }
    
    .reports-list.grid-view {
        grid-template-columns: 1fr;
    }
    
    .fab {
        bottom: var(--spacing-md);
        left: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .report-card {
        padding: var(--spacing-md);
    }
    
    .report-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .report-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    .modal-content {
        width: 95%;
        margin: var(--spacing-sm);
    }
}
